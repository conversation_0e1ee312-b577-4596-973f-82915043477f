using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[CustomEditor(typeof(Prefabpaintertool))]
public class SimpleGridPrefabPainterEditor : Editor
{
    private Prefabpaintertool painter;
    private bool isCreatingGrid = false;
    private bool isResizingGrid = false;
    private Vector3 gridStartPos;
    private Vector3 gridEndPos;
    private Vector3 gridCenter;
    private Vector2 gridSize = Vector2.one;
    private int rows = 1;
    private int columns = 1;
    private List<Vector3> gridPositions = new();

    // Resize handles
    private enum ResizeHandle { None, TopLeft, TopRight, BottomLeft, BottomRight, Top, Bottom, Left, Right }
    private ResizeHandle activeHandle = ResizeHandle.None;

    // Single brush variables
    private Vector3 singleBrushPosition;
    private bool showSingleBrushPreview = false;
    private bool isContinuousPlacing = false;
    private Vector3 lastPlacedPosition;

    // Physics brush variables
    private Vector3 physicsBrushPosition;
    private Vector3 physicsDropPosition;
    private bool showPhysicsBrushPreview = false;
    private bool isPhysicsPlacing = false;
    private List<GameObject> simulatingObjects = new();
    private Dictionary<GameObject, EditorApplication.CallbackFunction> simulationCallbacks = new();

    // Store original physics states to prevent affecting existing objects
    private Dictionary<Rigidbody, bool> originalKinematicStates = new();
    private Dictionary<Rigidbody, bool> originalGravityStates = new();

    // Spline brush variables
    private List<Vector3> splinePoints = new();
    private bool isCreatingSpline = false;
    private bool showSplinePreview = false;
    private Vector3 currentSplinePoint;
    private List<Vector3> splinePositions = new();
    private bool splineComplete = false;

    // Shape brush variables
    private bool isCreatingShape = false;
    private bool showShapePreview = false;
    private Vector3 shapeStartPos;
    private Vector3 shapeEndPos;
    private Vector3 shapeCenter;
    private List<Vector3> shapePositions = new();
    private Vector3 currentShapePos;
    
    void OnEnable()
    {
        painter = (Prefabpaintertool)target;
        SceneView.duringSceneGui += OnSceneGUI;

        // Reset physics simulation state when tool is enabled
        ResetPhysicsSimulation();
    }
    
    void OnDisable()
    {
        SceneView.duringSceneGui -= OnSceneGUI;

        // Stop all physics simulations when tool is disabled
        if (simulatingObjects.Count > 0)
        {
            StopAllPhysicsSimulations();
        }

        // Ensure original physics states are restored
        RestoreOriginalPhysicsStates();
    }

    public override void OnInspectorGUI()
    {
        // Draw title header
        DrawTitleHeader();

        // Draw custom inspector with conditional visibility
        DrawConditionalInspector();

        EditorGUILayout.Space();

        // Brush Mode Instructions
        if (painter.brushMode == BrushMode.Grid)
        {
            EditorGUILayout.HelpBox(
                "GRID MODE:\n" +
                "• Hold Shift + Click & Drag to create grid\n" +
                "• Drag resize handles to adjust grid size\n" +
                "• Press Enter to place prefabs\n" +
                "• Press Escape to cancel",
                MessageType.Info);
        }
        else if (painter.brushMode == BrushMode.Single)
        {
            EditorGUILayout.HelpBox(
                "SINGLE BRUSH MODE:\n" +
                "• Hold Shift + Click to place single prefab\n" +
                "• Hold Shift + Click & Drag for continuous placement\n" +
                "• Hover to see preview with crosshair\n" +
                "• Prefabs are properly centered and parented\n" +
                "• Adjust Min Placement Distance to control spacing",
                MessageType.Info);
        }
        else if (painter.brushMode == BrushMode.Physics)
        {
            EditorGUILayout.HelpBox(
                "🎯 ADVANCED PHYSICS BRUSH MODE:\n" +
                "• Only places prefabs with Collider and Rigidbody\n" +
                "• Hold Shift + Click/Drag for physics placement\n" +
                "• Prefabs drop with REALISTIC PHYSICS SIMULATION\n" +
                "• Random forces, torque, wind, and tumble effects\n" +
                "• Variable mass, bouncy materials, and friction\n" +
                "• Objects fall, bounce, tumble, and settle naturally\n" +
                "• Enable 'Auto Stop Physics' to freeze after time\n" +
                "• Red preview shows drop zone and landing area\n" +
                "• Adjust Force/Torque Intensity for more chaos!",
                MessageType.Warning);
        }
        else if (painter.brushMode == BrushMode.Spline)
        {
            EditorGUILayout.HelpBox(
                "🛤️ SPLINE FENCE BRUSH MODE:\n" +
                "• Perfect for creating fences, walls, and paths\n" +
                "• Hold Shift + Click to add spline points\n" +
                "• Creates smooth curved paths between points\n" +
                "• Auto-rotates prefabs to follow path direction\n" +
                "• Adjustable spacing and spline resolution\n" +
                "• Press Enter to place prefabs along spline\n" +
                "• Press Escape to cancel spline creation\n" +
                "• Blue preview shows spline path and placement points\n" +
                "• Enable 'Snap to Ground' for terrain following",
                MessageType.Info);
        }
        else if (painter.brushMode == BrushMode.Square)
        {
            EditorGUILayout.HelpBox(
                "⬜ SQUARE BRUSH MODE:\n" +
                "• Perfect for creating square formations\n" +
                "• Hold Shift + Click & Drag to define square size\n" +
                "• Toggle 'Fill Shape' for solid square or outline only\n" +
                "• Adjustable spacing between objects\n" +
                "• Press Enter to place prefabs in square formation\n" +
                "• Press Escape to cancel square creation\n" +
                "• Cyan preview shows square bounds and placement points",
                MessageType.Info);
        }
        else if (painter.brushMode == BrushMode.Rectangle)
        {
            EditorGUILayout.HelpBox(
                "▭ RECTANGLE BRUSH MODE:\n" +
                "• Perfect for creating rectangular formations\n" +
                "• Hold Shift + Click & Drag to define rectangle size\n" +
                "• Toggle 'Fill Shape' for solid rectangle or outline only\n" +
                "• Separate width and height controls\n" +
                "• Press Enter to place prefabs in rectangle formation\n" +
                "• Press Escape to cancel rectangle creation\n" +
                "• Cyan preview shows rectangle bounds and placement points",
                MessageType.Info);
        }
        else if (painter.brushMode == BrushMode.Circle)
        {
            EditorGUILayout.HelpBox(
                "⭕ CIRCLE BRUSH MODE:\n" +
                "• Perfect for creating circular formations\n" +
                "• Hold Shift + Click & Drag to define circle radius\n" +
                "• Toggle 'Fill Shape' for solid circle or outline only\n" +
                "• Adjustable circle segments for smoothness\n" +
                "• Press Enter to place prefabs in circle formation\n" +
                "• Press Escape to cancel circle creation\n" +
                "• Cyan preview shows circle bounds and placement points",
                MessageType.Info);
        }

        EditorGUILayout.Space();

        // Prefab Selection Info
        if (painter.prefabsToPlace != null && painter.prefabsToPlace.Length > 0)
        {
            EditorGUILayout.LabelField($"Selected Prefab: {painter.selectedPrefabIndex + 1}/{painter.prefabsToPlace.Length}");
            if (painter.selectedPrefabIndex < painter.prefabsToPlace.Length && painter.prefabsToPlace[painter.selectedPrefabIndex] != null)
            {
                EditorGUILayout.LabelField($"Current: {painter.prefabsToPlace[painter.selectedPrefabIndex].name}");
            }
        }

        EditorGUILayout.Space();

        // Grid Spacing Controls
        EditorGUILayout.LabelField("Grid Spacing", EditorStyles.boldLabel);
        EditorGUILayout.BeginHorizontal();
        painter.rowSpacing = EditorGUILayout.FloatField("Row Spacing", painter.rowSpacing);
        painter.columnSpacing = EditorGUILayout.FloatField("Column Spacing", painter.columnSpacing);
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space();

        // Custom Dimensions
        painter.useCustomDimensions = EditorGUILayout.Toggle("Use Custom Dimensions", painter.useCustomDimensions);
        if (painter.useCustomDimensions)
        {
            EditorGUILayout.BeginHorizontal();
            painter.customRows = EditorGUILayout.IntField("Rows", painter.customRows);
            painter.customColumns = EditorGUILayout.IntField("Columns", painter.customColumns);
            EditorGUILayout.EndHorizontal();
        }

        EditorGUILayout.Space();

        // Multiple Prefab Options
        EditorGUILayout.LabelField("Multiple Prefab Options", EditorStyles.boldLabel);
        painter.placeMultiplePrefabs = EditorGUILayout.Toggle("Place Multiple Prefabs", painter.placeMultiplePrefabs);
        if (painter.placeMultiplePrefabs)
        {
            painter.randomPrefabSelection = EditorGUILayout.Toggle("Random Prefab Selection", painter.randomPrefabSelection);
        }

        EditorGUILayout.Space();

        // Rotation Options
        EditorGUILayout.LabelField("Rotation Options", EditorStyles.boldLabel);
        painter.enableRotation = EditorGUILayout.Toggle("Enable Rotation", painter.enableRotation);
        if (painter.enableRotation)
        {
            painter.baseRotation = EditorGUILayout.Vector3Field("Base Rotation", painter.baseRotation);
            painter.randomRotation = EditorGUILayout.Toggle("Random Rotation", painter.randomRotation);

            if (painter.randomRotation)
            {
                painter.randomRotationRange = EditorGUILayout.Vector3Field("Random Range", painter.randomRotationRange);
                EditorGUILayout.HelpBox("Random rotation range in degrees (X, Y, Z)", MessageType.Info);
            }
            else
            {
                painter.rotationStep = EditorGUILayout.FloatField("Rotation Step", painter.rotationStep);
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Rotate +"))
                {
                    painter.baseRotation.y += painter.rotationStep;
                }
                if (GUILayout.Button("Rotate -"))
                {
                    painter.baseRotation.y -= painter.rotationStep;
                }
                if (GUILayout.Button("Reset"))
                {
                    painter.baseRotation = Vector3.zero;
                }
                EditorGUILayout.EndHorizontal();
            }
        }

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Instructions:", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("1. Hold Shift + Left Click and drag to create grid");
        if (painter.enableGridResize)
        {
            EditorGUILayout.LabelField("2. Drag yellow handles to resize grid");
        }
        EditorGUILayout.LabelField($"{(painter.enableGridResize ? "3" : "2")}. Press Enter to place prefabs in grid");
        EditorGUILayout.LabelField($"{(painter.enableGridResize ? "4" : "3")}. Press Escape to cancel grid creation");
        if (painter.enableRotation)
        {
            EditorGUILayout.LabelField($"{(painter.enableGridResize ? "5" : "4")}. Press R/T to rotate during grid creation");
            EditorGUILayout.LabelField($"{(painter.enableGridResize ? "6" : "5")}. Press Y to reset rotation");
        }

        EditorGUILayout.Space();

        // Grid Resize Option
        painter.enableGridResize = EditorGUILayout.Toggle("Enable Grid Resize", painter.enableGridResize);
        if (painter.enableGridResize)
        {
            painter.resizeHandleColor = EditorGUILayout.ColorField("Resize Handle Color", painter.resizeHandleColor);
        }

        if (isCreatingGrid)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField($"Grid Size: {rows} x {columns}", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Total Blocks: {rows * columns}");
            if (painter.enableGridResize)
            {
                EditorGUILayout.LabelField($"Grid Dimensions: {gridSize.x:F1} x {gridSize.y:F1}");
                if (isResizingGrid)
                {
                    EditorGUILayout.LabelField("🔄 Resizing Grid...", EditorStyles.boldLabel);
                }
            }
        }

        EditorGUILayout.Space();

        // Physics Simulation Controls (only show in Physics mode)
        if (painter.brushMode == BrushMode.Physics)
        {
            EditorGUILayout.LabelField("Physics Simulation Controls", EditorStyles.boldLabel);

            // Show detailed simulation status
            if (simulatingObjects.Count > 0)
            {
                EditorGUILayout.HelpBox($"🎲 PHYSICS ACTIVE: {simulatingObjects.Count} objects simulating with consistent behavior!", MessageType.Info);

                // Show current settings being used
                EditorGUILayout.LabelField($"⚙️ Force: {painter.forceIntensity:F1} | Torque: {painter.torqueIntensity:F1} | Wind: {painter.windStrength:F1}");
                if (painter.addRandomBounce)
                    EditorGUILayout.LabelField($"🏀 Bounciness: {painter.bounciness:F2} | Mass Range: {painter.minMass:F1}-{painter.maxMass:F1}");

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("⏹️ Stop All Physics"))
                {
                    StopAllPhysicsSimulations();
                }
                if (GUILayout.Button("🔄 Repaint Scene"))
                {
                    SceneView.RepaintAll();
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("🔧 Reset Physics System"))
                {
                    ResetPhysicsSimulation();
                }
                EditorGUILayout.EndHorizontal();
            }
            else
            {
                EditorGUILayout.HelpBox("⚡ Ready for CONSISTENT physics simulation - same settings = same behavior!", MessageType.None);
                EditorGUILayout.LabelField("💡 Tip: Objects at same position will behave identically with current settings");
            }

            EditorGUILayout.Space();
        }

        // Spline Brush Controls (only show in Spline mode)
        if (painter.brushMode == BrushMode.Spline)
        {
            EditorGUILayout.LabelField("Spline Brush Controls", EditorStyles.boldLabel);

            // Show spline creation status
            if (isCreatingSpline)
            {
                EditorGUILayout.HelpBox($"🛤️ Creating spline with {splinePoints.Count} points", MessageType.Info);

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("✅ Complete Spline (Enter)"))
                {
                    CompleteSpline();
                }
                if (GUILayout.Button("❌ Cancel Spline (Esc)"))
                {
                    CancelSpline();
                }
                EditorGUILayout.EndHorizontal();

                if (splinePoints.Count >= 2)
                {
                    EditorGUILayout.LabelField($"📏 Estimated prefabs: {splinePositions.Count}");
                }
            }
            else
            {
                EditorGUILayout.HelpBox("🎯 Ready to create spline - Hold Shift + Click to add points!", MessageType.None);
            }

            EditorGUILayout.Space();
        }

        // Shape Brush Controls (only show in Shape modes)
        if (painter.brushMode == BrushMode.Square ||
            painter.brushMode == BrushMode.Rectangle ||
            painter.brushMode == BrushMode.Circle)
        {
            EditorGUILayout.LabelField("Shape Brush Controls", EditorStyles.boldLabel);

            // Show shape creation status
            if (isCreatingShape)
            {
                EditorGUILayout.HelpBox($"🔷 Creating {painter.brushMode} - drag to size, Enter to place!", MessageType.Info);

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("✅ Complete Shape (Enter)"))
                {
                    CompleteShape();
                }
                if (GUILayout.Button("❌ Cancel Shape (Esc)"))
                {
                    CancelShape();
                }
                EditorGUILayout.EndHorizontal();

                if (shapePositions.Count > 0)
                {
                    string fillMode = painter.fillShape ? "Filled" : "Outline";
                    EditorGUILayout.LabelField($"📊 {fillMode}: {shapePositions.Count} prefabs");
                }
            }
            else
            {
                EditorGUILayout.HelpBox("🎯 Ready to create shape - Hold Shift + Click & Drag!", MessageType.None);
            }

            EditorGUILayout.Space();
        }

        EditorGUILayout.HelpBox(
            "Make sure to assign prefabs to place and set the ground layer for surface detection.",
            MessageType.Info);
    }

    void DrawTitleHeader()
    {
        EditorGUILayout.Space(12);

        // Ultra-modern gradient background with premium styling
        GUIStyle premiumBoxStyle = new GUIStyle(GUI.skin.box)
        {
            normal = { background = MakeAdvancedGradientTexture(400, 110,
                new Color(0.08f, 0.15f, 0.35f, 1f),    // Deep midnight blue
                new Color(0.15f, 0.35f, 0.65f, 1f),    // Electric blue
                new Color(0.25f, 0.45f, 0.85f, 1f),    // Bright cyan blue
                new Color(0.45f, 0.25f, 0.75f, 1f),    // Purple accent
                new Color(0.65f, 0.15f, 0.55f, 1f)) }, // Magenta highlight
            border = new RectOffset(10, 10, 10, 10),
            padding = new RectOffset(12, 12, 15, 15),
            margin = new RectOffset(4, 4, 6, 6)
        };

        // Spectacular title style with glow effect
        GUIStyle spectacularTitleStyle = new GUIStyle(EditorStyles.boldLabel)
        {
            fontSize = 16,
            fontStyle = FontStyle.Bold,
            alignment = TextAnchor.MiddleCenter,
            normal = { textColor = new Color(1f, 1f, 1f, 1f) }, // Pure brilliant white
            padding = new RectOffset(0, 0, 5, 5),
            wordWrap = true
        };

        // Enhanced subtitle with gradient text effect
        GUIStyle enhancedSubtitleStyle = new GUIStyle(EditorStyles.boldLabel)
        {
            fontSize = 13,
            fontStyle = FontStyle.BoldAndItalic,
            alignment = TextAnchor.MiddleCenter,
            normal = { textColor = new Color(0.85f, 0.95f, 1f, 0.95f) }, // Bright white-cyan
            padding = new RectOffset(0, 0, 3, 3)
        };

        // Premium developer credit style - Ultra prominent
        GUIStyle premiumCreditStyle = new GUIStyle(EditorStyles.boldLabel)
        {
            fontSize = 15,
            fontStyle = FontStyle.Bold,
            alignment = TextAnchor.MiddleCenter,
            normal = { textColor = new Color(1f, 0.85f, 0.1f, 1f) }, // Brilliant gold
            padding = new RectOffset(0, 0, 3, 3),
            wordWrap = false
        };

        // Developer label style
        GUIStyle developerLabelStyle = new GUIStyle(EditorStyles.boldLabel)
        {
            fontSize = 11,
            fontStyle = FontStyle.Bold,
            alignment = TextAnchor.MiddleCenter,
            normal = { textColor = new Color(0.9f, 0.9f, 1f, 0.9f) }, // Bright white
            padding = new RectOffset(0, 0, 1, 1)
        };

        // Version/status style for extra flair
        GUIStyle versionStyle = new GUIStyle(EditorStyles.boldLabel)
        {
            fontSize = 10,
            fontStyle = FontStyle.Bold,
            alignment = TextAnchor.MiddleCenter,
            normal = { textColor = new Color(0.3f, 1f, 0.3f, 0.8f) }, // Bright green
            padding = new RectOffset(0, 0, 2, 2)
        };

        // Create the premium header container with shadow effect
        EditorGUILayout.BeginVertical(premiumBoxStyle);

        EditorGUILayout.Space(10);

        // Animated title with multiple emojis
        string animatedTitle = GetAnimatedTitle();
        EditorGUILayout.LabelField(animatedTitle, spectacularTitleStyle);

        EditorGUILayout.Space(4);

        // Enhanced subtitle with style
        EditorGUILayout.LabelField("🚀 Unity Grid Creator Tool 🚀", enhancedSubtitleStyle);

        EditorGUILayout.Space(2);

        // Version/status indicator
        EditorGUILayout.LabelField("✅ Professional Edition ✅", versionStyle);

        EditorGUILayout.Space(4);

        // Attractive developer credit in one line
        EditorGUILayout.LabelField("🌟 ALI TAJ 🌟", premiumCreditStyle);

        EditorGUILayout.Space(12);

        EditorGUILayout.EndVertical();

        EditorGUILayout.Space(15);
    }

    string GetAnimatedTitle()
    {
        // Create animated title with rotating emojis and effects
        float time = (float)EditorApplication.timeSinceStartup;

        // Rotating emoji selection
        string[] emojis = { "🎯", "⚡", "🔥", "💎", "🌟", "🚀", "✨", "🎨" };
        int emojiIndex = Mathf.FloorToInt(time * 2f) % emojis.Length;
        string currentEmoji = emojis[emojiIndex];

        // Shorter title variations that fit better
        string[] titleVariations = {
            $"{currentEmoji} GRID PREFAB PAINTER {currentEmoji}",
            $"✨ PREFAB PAINTER PRO ✨",
            $"🔥 GRID PAINTER STUDIO 🔥",
            $"💎 PREFAB TOOL PRO 💎"
        };

        int titleIndex = Mathf.FloorToInt(time * 0.5f) % titleVariations.Length;

        // Force repaint for animation
        if (EditorApplication.isPlaying || time % 0.5f < 0.1f)
        {
            SceneView.RepaintAll();
        }

        return titleVariations[titleIndex];
    }

    Texture2D MakeTexture(int width, int height, Color color)
    {
        Color[] pix = new Color[width * height];
        for (int i = 0; i < pix.Length; i++)
            pix[i] = color;

        Texture2D result = new Texture2D(width, height);
        result.SetPixels(pix);
        result.Apply();
        return result;
    }

    Texture2D MakeGradientTexture(int width, int height, Color topColor, Color middleColor, Color bottomColor)
    {
        Color[] pixels = new Color[width * height];

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                // Calculate vertical gradient position (0 to 1)
                float gradientPos = (float)y / (height - 1);

                // Create a multi-stop gradient
                Color pixelColor;
                if (gradientPos <= 0.5f)
                {
                    // Top half: blend from top to middle
                    float t = gradientPos * 2f; // 0 to 1
                    pixelColor = Color.Lerp(topColor, middleColor, t);
                }
                else
                {
                    // Bottom half: blend from middle to bottom
                    float t = (gradientPos - 0.5f) * 2f; // 0 to 1
                    pixelColor = Color.Lerp(middleColor, bottomColor, t);
                }

                // Add subtle horizontal variation for depth
                float horizontalVariation = Mathf.Sin((float)x / width * Mathf.PI * 2f) * 0.02f;
                pixelColor.r = Mathf.Clamp01(pixelColor.r + horizontalVariation);
                pixelColor.g = Mathf.Clamp01(pixelColor.g + horizontalVariation);
                pixelColor.b = Mathf.Clamp01(pixelColor.b + horizontalVariation);

                // Add subtle noise for texture
                float noise = (Mathf.PerlinNoise(x * 0.1f, y * 0.1f) - 0.5f) * 0.03f;
                pixelColor.r = Mathf.Clamp01(pixelColor.r + noise);
                pixelColor.g = Mathf.Clamp01(pixelColor.g + noise);
                pixelColor.b = Mathf.Clamp01(pixelColor.b + noise);

                pixels[y * width + x] = pixelColor;
            }
        }

        Texture2D result = new Texture2D(width, height);
        result.SetPixels(pixels);
        result.Apply();
        return result;
    }

    Texture2D MakeAdvancedGradientTexture(int width, int height, Color color1, Color color2, Color color3, Color color4, Color color5)
    {
        Color[] pixels = new Color[width * height];

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                // Calculate vertical gradient position (0 to 1)
                float gradientPos = (float)y / (height - 1);

                // Create a 5-stop advanced gradient with smooth transitions
                Color pixelColor;
                if (gradientPos <= 0.25f)
                {
                    // First quarter: color1 to color2
                    float t = gradientPos * 4f;
                    pixelColor = Color.Lerp(color1, color2, t);
                }
                else if (gradientPos <= 0.5f)
                {
                    // Second quarter: color2 to color3
                    float t = (gradientPos - 0.25f) * 4f;
                    pixelColor = Color.Lerp(color2, color3, t);
                }
                else if (gradientPos <= 0.75f)
                {
                    // Third quarter: color3 to color4
                    float t = (gradientPos - 0.5f) * 4f;
                    pixelColor = Color.Lerp(color3, color4, t);
                }
                else
                {
                    // Final quarter: color4 to color5
                    float t = (gradientPos - 0.75f) * 4f;
                    pixelColor = Color.Lerp(color4, color5, t);
                }

                // Add advanced horizontal wave variation for premium depth
                float horizontalWave1 = Mathf.Sin((float)x / width * Mathf.PI * 3f) * 0.03f;
                float horizontalWave2 = Mathf.Cos((float)x / width * Mathf.PI * 5f) * 0.02f;
                float combinedWave = horizontalWave1 + horizontalWave2;

                pixelColor.r = Mathf.Clamp01(pixelColor.r + combinedWave);
                pixelColor.g = Mathf.Clamp01(pixelColor.g + combinedWave);
                pixelColor.b = Mathf.Clamp01(pixelColor.b + combinedWave);

                // Add premium noise texture with multiple octaves
                float noise1 = Mathf.PerlinNoise(x * 0.08f, y * 0.08f) * 0.04f;
                float noise2 = Mathf.PerlinNoise(x * 0.15f, y * 0.15f) * 0.02f;
                float combinedNoise = (noise1 + noise2 - 0.03f);

                pixelColor.r = Mathf.Clamp01(pixelColor.r + combinedNoise);
                pixelColor.g = Mathf.Clamp01(pixelColor.g + combinedNoise);
                pixelColor.b = Mathf.Clamp01(pixelColor.b + combinedNoise);

                // Add subtle shimmer effect
                float shimmer = Mathf.Sin((x + y) * 0.1f) * 0.01f;
                pixelColor.r = Mathf.Clamp01(pixelColor.r + shimmer);
                pixelColor.g = Mathf.Clamp01(pixelColor.g + shimmer);
                pixelColor.b = Mathf.Clamp01(pixelColor.b + shimmer);

                pixels[y * width + x] = pixelColor;
            }
        }

        Texture2D result = new Texture2D(width, height);
        result.SetPixels(pixels);
        result.Apply();
        return result;
    }

    void DrawConditionalInspector()
    {
        serializedObject.Update();

        // Always show basic settings
        DrawBasicSettings();

        // Show brush mode specific settings
        switch (painter.brushMode)
        {
            case BrushMode.Grid:
                DrawGridSettings();
                break;
            case BrushMode.Single:
                DrawSingleBrushSettings();
                break;
            case BrushMode.Physics:
                DrawPhysicsBrushSettings();
                break;
            case BrushMode.Spline:
                DrawSplineBrushSettings();
                break;
            case BrushMode.Square:
            case BrushMode.Rectangle:
            case BrushMode.Circle:
                DrawShapeBrushSettings();
                break;
        }

        serializedObject.ApplyModifiedProperties();
    }

    void DrawBasicSettings()
    {
        EditorGUILayout.LabelField("Basic Settings", EditorStyles.boldLabel);

        // Brush Mode
        painter.brushMode = (BrushMode)EditorGUILayout.EnumPopup("Brush Mode", painter.brushMode);

        // Prefab Settings
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Prefab Settings", EditorStyles.boldLabel);

        SerializedProperty prefabsArray = serializedObject.FindProperty("prefabsToPlace");
        EditorGUILayout.PropertyField(prefabsArray, true);

        painter.selectedPrefabIndex = EditorGUILayout.IntSlider("Selected Prefab Index", painter.selectedPrefabIndex, 0,
            Mathf.Max(0, painter.prefabsToPlace != null ? painter.prefabsToPlace.Length - 1 : 0));

        painter.placeMultiplePrefabs = EditorGUILayout.Toggle("Place Multiple Prefabs", painter.placeMultiplePrefabs);
        if (painter.placeMultiplePrefabs)
        {
            painter.randomPrefabSelection = EditorGUILayout.Toggle("Random Prefab Selection", painter.randomPrefabSelection);
        }

        // Ground Layer
        EditorGUILayout.Space();
        painter.groundLayer = EditorGUILayout.LayerField("Ground Layer", painter.groundLayer);
    }

    void DrawGridSettings()
    {
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Grid Settings", EditorStyles.boldLabel);

        painter.gridSize = EditorGUILayout.FloatField("Grid Size", painter.gridSize);

        EditorGUILayout.BeginHorizontal();
        painter.rowSpacing = EditorGUILayout.FloatField("Row Spacing", painter.rowSpacing);
        painter.columnSpacing = EditorGUILayout.FloatField("Column Spacing", painter.columnSpacing);
        EditorGUILayout.EndHorizontal();

        painter.useCustomDimensions = EditorGUILayout.Toggle("Use Custom Dimensions", painter.useCustomDimensions);
        if (painter.useCustomDimensions)
        {
            EditorGUILayout.BeginHorizontal();
            painter.customRows = EditorGUILayout.IntField("Rows", painter.customRows);
            painter.customColumns = EditorGUILayout.IntField("Columns", painter.customColumns);
            EditorGUILayout.EndHorizontal();
        }

        // Visual Settings
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Visual Settings", EditorStyles.boldLabel);
        painter.showGridPreview = EditorGUILayout.Toggle("Show Grid Preview", painter.showGridPreview);
        painter.gridColor = EditorGUILayout.ColorField("Grid Color", painter.gridColor);
        painter.enableGridResize = EditorGUILayout.Toggle("Enable Grid Resize", painter.enableGridResize);
        if (painter.enableGridResize)
        {
            painter.resizeHandleColor = EditorGUILayout.ColorField("Resize Handle Color", painter.resizeHandleColor);
        }

        // Rotation Settings
        DrawRotationSettings();
    }

    void DrawSingleBrushSettings()
    {
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Single Brush Settings", EditorStyles.boldLabel);

        painter.enableBrushPreview = EditorGUILayout.Toggle("Enable Brush Preview", painter.enableBrushPreview);
        painter.brushPreviewColor = EditorGUILayout.ColorField("Brush Preview Color", painter.brushPreviewColor);
        painter.brushPreviewSize = EditorGUILayout.FloatField("Brush Preview Size", painter.brushPreviewSize);
        painter.minPlacementDistance = EditorGUILayout.Slider("Min Placement Distance", painter.minPlacementDistance, 0.1f, 5f);

        // Rotation Settings
        DrawRotationSettings();
    }

    void DrawPhysicsBrushSettings()
    {
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Physics Brush Settings", EditorStyles.boldLabel);

        painter.requireCollider = EditorGUILayout.Toggle("Require Collider", painter.requireCollider);
        painter.requireRigidbody = EditorGUILayout.Toggle("Require Rigidbody", painter.requireRigidbody);
        painter.physicsPreviewColor = EditorGUILayout.ColorField("Physics Preview Color", painter.physicsPreviewColor);
        painter.dropHeight = EditorGUILayout.Slider("Drop Height", painter.dropHeight, 0.5f, 20f);
        painter.showDropPreview = EditorGUILayout.Toggle("Show Drop Preview", painter.showDropPreview);

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Physics Simulation", EditorStyles.boldLabel);
        painter.enablePhysicsSimulation = EditorGUILayout.Toggle("Enable Physics", painter.enablePhysicsSimulation);

        if (painter.enablePhysicsSimulation)
        {
            painter.autoStopPhysics = EditorGUILayout.Toggle("Auto Stop Physics", painter.autoStopPhysics);
            if (painter.autoStopPhysics)
            {
                painter.simulationTime = EditorGUILayout.Slider("Simulation Time (s)", painter.simulationTime, 0.1f, 10f);
            }

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Advanced Physics", EditorStyles.boldLabel);
            painter.forceIntensity = EditorGUILayout.Slider("Force Intensity", painter.forceIntensity, 0f, 10f);
            painter.torqueIntensity = EditorGUILayout.Slider("Torque Intensity", painter.torqueIntensity, 0f, 10f);
            painter.addRandomBounce = EditorGUILayout.Toggle("Add Random Bounce", painter.addRandomBounce);
            if (painter.addRandomBounce)
            {
                painter.bounciness = EditorGUILayout.Slider("Bounciness", painter.bounciness, 0f, 1f);
            }

            painter.varyMass = EditorGUILayout.Toggle("Vary Mass", painter.varyMass);
            if (painter.varyMass)
            {
                EditorGUILayout.BeginHorizontal();
                painter.minMass = EditorGUILayout.FloatField("Min Mass", painter.minMass);
                painter.maxMass = EditorGUILayout.FloatField("Max Mass", painter.maxMass);
                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Environmental Effects", EditorStyles.boldLabel);
            painter.addWindEffect = EditorGUILayout.Toggle("Add Wind Effect", painter.addWindEffect);
            if (painter.addWindEffect)
            {
                painter.windStrength = EditorGUILayout.Slider("Wind Strength", painter.windStrength, 0f, 5f);
                painter.windDirection = EditorGUILayout.Vector3Field("Wind Direction", painter.windDirection);
            }

            painter.addTumbleEffect = EditorGUILayout.Toggle("Add Tumble Effect", painter.addTumbleEffect);
            if (painter.addTumbleEffect)
            {
                painter.tumbleChance = EditorGUILayout.Slider("Tumble Chance", painter.tumbleChance, 0f, 2f);
            }
        }

        // Rotation Settings
        DrawRotationSettings();
    }

    void DrawSplineBrushSettings()
    {
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Spline Brush Settings", EditorStyles.boldLabel);

        painter.splineColor = EditorGUILayout.ColorField("Spline Color", painter.splineColor);
        painter.splineSpacing = EditorGUILayout.Slider("Spline Spacing", painter.splineSpacing, 0.1f, 5f);
        painter.autoRotateToPath = EditorGUILayout.Toggle("Auto Rotate to Path", painter.autoRotateToPath);
        painter.smoothSpline = EditorGUILayout.Toggle("Smooth Spline", painter.smoothSpline);
        painter.splineWidth = EditorGUILayout.Slider("Spline Width", painter.splineWidth, 0.1f, 2f);
        painter.showSplinePreview = EditorGUILayout.Toggle("Show Spline Preview", painter.showSplinePreview);
        painter.snapToGround = EditorGUILayout.Toggle("Snap to Ground", painter.snapToGround);
        painter.splineResolution = EditorGUILayout.IntSlider("Spline Resolution", painter.splineResolution, 2, 20);
        painter.closedSpline = EditorGUILayout.Toggle("Closed Spline", painter.closedSpline);

        // Rotation Settings
        DrawRotationSettings();
    }

    void DrawShapeBrushSettings()
    {
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Shape Brush Settings", EditorStyles.boldLabel);

        painter.shapePreviewColor = EditorGUILayout.ColorField("Shape Preview Color", painter.shapePreviewColor);
        painter.fillShape = EditorGUILayout.Toggle("Fill Shape", painter.fillShape);
        painter.outlineOnly = EditorGUILayout.Toggle("Outline Only", painter.outlineOnly);
        painter.shapeSpacing = EditorGUILayout.Slider("Shape Spacing", painter.shapeSpacing, 0.1f, 3f);
        painter.shapeSize = EditorGUILayout.Slider("Shape Size", painter.shapeSize, 1f, 20f);
        painter.showShapePreview = EditorGUILayout.Toggle("Show Shape Preview", painter.showShapePreview);
        painter.snapShapeToGround = EditorGUILayout.Toggle("Snap Shape to Ground", painter.snapShapeToGround);

        // Shape-specific settings
        if (painter.brushMode == BrushMode.Rectangle)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Rectangle Settings", EditorStyles.boldLabel);
            painter.rectangleWidth = EditorGUILayout.Slider("Rectangle Width", painter.rectangleWidth, 1f, 20f);
            painter.rectangleHeight = EditorGUILayout.Slider("Rectangle Height", painter.rectangleHeight, 1f, 20f);
        }
        else if (painter.brushMode == BrushMode.Circle)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Circle Settings", EditorStyles.boldLabel);
            painter.circleRadius = EditorGUILayout.Slider("Circle Radius", painter.circleRadius, 1f, 20f);
            painter.circleSegments = EditorGUILayout.IntSlider("Circle Segments", painter.circleSegments, 3, 32);
        }

        // Rotation Settings
        DrawRotationSettings();
    }

    void DrawRotationSettings()
    {
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Rotation Settings", EditorStyles.boldLabel);

        painter.enableRotation = EditorGUILayout.Toggle("Enable Rotation", painter.enableRotation);
        if (painter.enableRotation)
        {
            painter.baseRotation = EditorGUILayout.Vector3Field("Base Rotation", painter.baseRotation);
            painter.randomRotation = EditorGUILayout.Toggle("Random Rotation", painter.randomRotation);

            if (painter.randomRotation)
            {
                painter.randomRotationRange = EditorGUILayout.Vector3Field("Random Range", painter.randomRotationRange);
                EditorGUILayout.HelpBox("Random rotation range in degrees (X, Y, Z)", MessageType.Info);
            }
            else
            {
                painter.rotationStep = EditorGUILayout.FloatField("Rotation Step", painter.rotationStep);
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Rotate +"))
                {
                    painter.baseRotation.y += painter.rotationStep;
                }
                if (GUILayout.Button("Rotate -"))
                {
                    painter.baseRotation.y -= painter.rotationStep;
                }
                if (GUILayout.Button("Reset"))
                {
                    painter.baseRotation = Vector3.zero;
                }
                EditorGUILayout.EndHorizontal();
            }
        }
    }
    
    void OnSceneGUI(SceneView sceneView)
    {
        Event e = Event.current;

        // Handle different brush modes
        if (painter.brushMode == BrushMode.Grid)
        {
            HandleGridMode(e);
        }
        else if (painter.brushMode == BrushMode.Single)
        {
            HandleSingleBrushMode(e);
        }
        else if (painter.brushMode == BrushMode.Physics)
        {
            HandlePhysicsBrushMode(e);
        }
        else if (painter.brushMode == BrushMode.Spline)
        {
            HandleSplineBrushMode(e);
        }
        else if (painter.brushMode == BrushMode.Square ||
                 painter.brushMode == BrushMode.Rectangle ||
                 painter.brushMode == BrushMode.Circle)
        {
            HandleShapeBrushMode(e);
        }

        // Draw previews
        DrawPreviews();
    }

    void HandleGridMode(Event e)
    {
        // Handle cursor changes for resize handles
        if (isCreatingGrid && painter.enableGridResize)
        {
            ResizeHandle handle = GetResizeHandleAtPosition(e.mousePosition);
            SetCursorForHandle(handle);
        }

        // Only create grid when Shift + Left Click + Drag
        if (e.type == EventType.MouseDown && e.button == 0 && e.shift)
        {
            // Check if clicking on resize handle first
            if (isCreatingGrid && painter.enableGridResize)
            {
                activeHandle = GetResizeHandleAtPosition(e.mousePosition);
                if (activeHandle != ResizeHandle.None)
                {
                    isResizingGrid = true;
                    e.Use();
                    return;
                }
            }

            // Create new grid
            Vector3 worldPos = GetWorldPosition(e.mousePosition);
            if (worldPos != Vector3.zero)
            {
                gridStartPos = worldPos;
                gridEndPos = worldPos;
                isCreatingGrid = true;
                CalculateInitialGridSize();
                e.Use();
            }
        }

        // Handle mouse drag
        if (e.type == EventType.MouseDrag && e.button == 0)
        {
            if (isResizingGrid && activeHandle != ResizeHandle.None)
            {
                // Handle grid resizing
                Vector3 worldPos = GetWorldPosition(e.mousePosition);
                if (worldPos != Vector3.zero)
                {
                    ResizeGrid(worldPos);
                    e.Use();
                }
            }
            else if (isCreatingGrid && e.shift)
            {
                // Continue creating grid only while dragging with Shift held
                Vector3 worldPos = GetWorldPosition(e.mousePosition);
                if (worldPos != Vector3.zero)
                {
                    gridEndPos = worldPos;
                    UpdateGridFromDrag();
                    e.Use();
                }
            }
        }

        // Handle mouse up
        if (e.type == EventType.MouseUp && e.button == 0)
        {
            if (isResizingGrid)
            {
                isResizingGrid = false;
                activeHandle = ResizeHandle.None;
                e.Use();
            }
            else if (isCreatingGrid)
            {
                e.Use();
            }
        }
        
        // Place prefabs when Enter is pressed
        if (e.type == EventType.KeyDown && e.keyCode == KeyCode.Return && isCreatingGrid)
        {
            PlacePrefabsInGrid();
            isCreatingGrid = false;
            isResizingGrid = false;
            e.Use();
        }

        // Cancel grid creation when Escape is pressed
        if (e.type == EventType.KeyDown && e.keyCode == KeyCode.Escape && isCreatingGrid)
        {
            isCreatingGrid = false;
            isResizingGrid = false;
            gridPositions.Clear();
            e.Use();
        }

        // Rotation shortcuts during grid creation
        if (isCreatingGrid && e.type == EventType.KeyDown)
        {
            if (e.keyCode == KeyCode.R)
            {
                painter.baseRotation.y += painter.rotationStep;
                e.Use();
            }
            else if (e.keyCode == KeyCode.T)
            {
                painter.baseRotation.y -= painter.rotationStep;
                e.Use();
            }
            else if (e.keyCode == KeyCode.Y)
            {
                painter.baseRotation = Vector3.zero;
                e.Use();
            }
        }
        
        // Draw grid preview only when creating
        if (isCreatingGrid && painter.showGridPreview)
        {
            DrawGridPreview();

            // Draw resize handles if enabled
            if (painter.enableGridResize)
            {
                DrawResizeHandles();
            }
        }
        
        // Repaint scene view when creating grid
        if (isCreatingGrid)
        {
            SceneView.RepaintAll();
        }
    }

    void HandleSingleBrushMode(Event e)
    {
        // Update brush preview position
        Vector3 worldPos = GetWorldPosition(e.mousePosition);
        if (worldPos != Vector3.zero)
        {
            singleBrushPosition = worldPos;
            showSingleBrushPreview = true;
        }
        else
        {
            showSingleBrushPreview = false;
        }

        // Handle mouse down - start continuous placing
        if (e.type == EventType.MouseDown && e.button == 0 && e.shift && showSingleBrushPreview)
        {
            isContinuousPlacing = true;
            PlaceSinglePrefab(singleBrushPosition);
            lastPlacedPosition = singleBrushPosition;
            e.Use();
        }

        // Handle mouse drag - continuous placing
        if (e.type == EventType.MouseDrag && e.button == 0 && e.shift && isContinuousPlacing && showSingleBrushPreview)
        {
            // Only place if we've moved far enough from last placement
            float distance = Vector3.Distance(singleBrushPosition, lastPlacedPosition);
            if (distance >= painter.minPlacementDistance)
            {
                PlaceSinglePrefab(singleBrushPosition);
                lastPlacedPosition = singleBrushPosition;
            }
            e.Use();
        }

        // Handle mouse up - stop continuous placing
        if (e.type == EventType.MouseUp && e.button == 0)
        {
            isContinuousPlacing = false;
        }

        // Repaint scene view for preview
        if (showSingleBrushPreview || isContinuousPlacing)
        {
            SceneView.RepaintAll();
        }
    }

    void HandlePhysicsBrushMode(Event e)
    {
        // Update brush preview position
        Vector3 worldPos = GetWorldPosition(e.mousePosition);
        if (worldPos != Vector3.zero)
        {
            physicsBrushPosition = worldPos;
            physicsDropPosition = worldPos + Vector3.up * painter.dropHeight;
            showPhysicsBrushPreview = true;
        }
        else
        {
            showPhysicsBrushPreview = false;
        }

        // Handle mouse down - start physics placing
        if (e.type == EventType.MouseDown && e.button == 0 && e.shift && showPhysicsBrushPreview)
        {
            if (CanPlacePhysicsPrefab())
            {
                isPhysicsPlacing = true;
                PlacePhysicsPrefab(physicsDropPosition, physicsBrushPosition);
                lastPlacedPosition = physicsBrushPosition;
            }
            e.Use();
        }

        // Handle mouse drag - continuous physics placing
        if (e.type == EventType.MouseDrag && e.button == 0 && e.shift && isPhysicsPlacing && showPhysicsBrushPreview)
        {
            if (CanPlacePhysicsPrefab())
            {
                // Only place if we've moved far enough from last placement
                float distance = Vector3.Distance(physicsBrushPosition, lastPlacedPosition);
                if (distance >= painter.minPlacementDistance)
                {
                    PlacePhysicsPrefab(physicsDropPosition, physicsBrushPosition);
                    lastPlacedPosition = physicsBrushPosition;
                }
            }
            e.Use();
        }

        // Handle mouse up - stop physics placing
        if (e.type == EventType.MouseUp && e.button == 0)
        {
            isPhysicsPlacing = false;
        }

        // Repaint scene view for preview
        if (showPhysicsBrushPreview || isPhysicsPlacing)
        {
            SceneView.RepaintAll();
        }
    }

    void HandleSplineBrushMode(Event e)
    {
        // Update current spline point position
        Vector3 worldPos = GetWorldPosition(e.mousePosition);
        if (worldPos != Vector3.zero)
        {
            currentSplinePoint = worldPos;
            showSplinePreview = true;
        }
        else
        {
            showSplinePreview = false;
        }

        // Handle mouse down - add spline point
        if (e.type == EventType.MouseDown && e.button == 0 && e.shift && showSplinePreview)
        {
            splinePoints.Add(currentSplinePoint);
            isCreatingSpline = true;
            e.Use();
        }

        // Handle Enter key - complete spline and place prefabs
        if (e.type == EventType.KeyDown && e.keyCode == KeyCode.Return && isCreatingSpline)
        {
            CompleteSpline();
            e.Use();
        }

        // Handle Escape key - cancel spline creation
        if (e.type == EventType.KeyDown && e.keyCode == KeyCode.Escape && isCreatingSpline)
        {
            CancelSpline();
            e.Use();
        }

        // Repaint scene view for preview
        if (showSplinePreview || isCreatingSpline)
        {
            SceneView.RepaintAll();
        }
    }

    void HandleShapeBrushMode(Event e)
    {
        // Update current shape position
        Vector3 worldPos = GetWorldPosition(e.mousePosition);
        if (worldPos != Vector3.zero)
        {
            currentShapePos = worldPos;
            showShapePreview = true;

            // Update shape end position if creating
            if (isCreatingShape)
            {
                shapeEndPos = currentShapePos;
                shapeCenter = (shapeStartPos + shapeEndPos) / 2f;
                GenerateShapePositions();
            }
        }
        else
        {
            showShapePreview = false;
        }

        // Handle mouse down - start shape creation
        if (e.type == EventType.MouseDown && e.button == 0 && e.shift && showShapePreview)
        {
            shapeStartPos = currentShapePos;
            shapeEndPos = currentShapePos;
            shapeCenter = currentShapePos;
            isCreatingShape = true;
            e.Use();
        }

        // Handle mouse up - finish shape sizing
        if (e.type == EventType.MouseUp && e.button == 0 && isCreatingShape)
        {
            // Shape is ready for placement
            e.Use();
        }

        // Handle Enter key - complete shape and place prefabs
        if (e.type == EventType.KeyDown && e.keyCode == KeyCode.Return && isCreatingShape)
        {
            CompleteShape();
            e.Use();
        }

        // Handle Escape key - cancel shape creation
        if (e.type == EventType.KeyDown && e.keyCode == KeyCode.Escape && isCreatingShape)
        {
            CancelShape();
            e.Use();
        }

        // Repaint scene view for preview
        if (showShapePreview || isCreatingShape)
        {
            SceneView.RepaintAll();
        }
    }

    void DrawPreviews()
    {
        if (painter.brushMode == BrushMode.Grid && isCreatingGrid)
        {
            DrawGridPreview();
        }
        else if (painter.brushMode == BrushMode.Single && showSingleBrushPreview)
        {
            DrawSingleBrushPreview();
        }
        else if (painter.brushMode == BrushMode.Physics && showPhysicsBrushPreview)
        {
            DrawPhysicsBrushPreview();
        }
        else if (painter.brushMode == BrushMode.Spline && (showSplinePreview || isCreatingSpline))
        {
            DrawSplinePreview();
        }
        else if ((painter.brushMode == BrushMode.Square ||
                  painter.brushMode == BrushMode.Rectangle ||
                  painter.brushMode == BrushMode.Circle) &&
                 (showShapePreview || isCreatingShape))
        {
            DrawShapePreview();
        }
    }
    
    Vector3 GetWorldPosition(Vector2 mousePosition)
    {
        Ray ray = HandleUtility.GUIPointToWorldRay(mousePosition);
        
        if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, painter.groundLayer))
        {
            return hit.point;
        }
        
        return Vector3.zero;
    }

    void CompleteSpline()
    {
        if (splinePoints.Count < 2)
        {
            Debug.LogWarning("Need at least 2 points to create a spline");
            return;
        }

        // Generate spline positions
        GenerateSplinePositions();

        // Place prefabs along spline
        PlacePrefabsAlongSpline();

        // Reset spline state
        CancelSpline();
    }

    void CancelSpline()
    {
        splinePoints.Clear();
        splinePositions.Clear();
        isCreatingSpline = false;
        splineComplete = false;
        showSplinePreview = false;
        SceneView.RepaintAll();
    }

    void GenerateSplinePositions()
    {
        splinePositions.Clear();

        if (splinePoints.Count < 2) return;

        // Calculate total spline length for proper spacing
        float totalLength = 0f;
        for (int i = 0; i < splinePoints.Count - 1; i++)
        {
            totalLength += Vector3.Distance(splinePoints[i], splinePoints[i + 1]);
        }

        // Calculate number of segments based on spacing
        int segments = Mathf.Max(1, Mathf.RoundToInt(totalLength / painter.splineSpacing));

        // Generate positions along spline
        for (int i = 0; i <= segments; i++)
        {
            float t = (float)i / segments;
            Vector3 position = GetSplinePosition(t);

            // Snap to ground if enabled
            if (painter.snapToGround)
            {
                position = SnapToGround(position);
            }

            splinePositions.Add(position);
        }
    }

    Vector3 GetSplinePosition(float t)
    {
        if (splinePoints.Count < 2) return Vector3.zero;

        if (painter.smoothSpline && splinePoints.Count >= 3)
        {
            // Use Catmull-Rom spline for smooth curves
            return GetCatmullRomPosition(t);
        }
        else
        {
            // Use linear interpolation for simple lines
            return GetLinearPosition(t);
        }
    }

    Vector3 GetLinearPosition(float t)
    {
        if (splinePoints.Count < 2) return Vector3.zero;

        float scaledT = t * (splinePoints.Count - 1);
        int index = Mathf.FloorToInt(scaledT);
        float localT = scaledT - index;

        if (index >= splinePoints.Count - 1)
        {
            return splinePoints[splinePoints.Count - 1];
        }

        return Vector3.Lerp(splinePoints[index], splinePoints[index + 1], localT);
    }

    Vector3 GetCatmullRomPosition(float t)
    {
        if (splinePoints.Count < 3) return GetLinearPosition(t);

        float scaledT = t * (splinePoints.Count - 1);
        int index = Mathf.FloorToInt(scaledT);
        float localT = scaledT - index;

        // Get control points for Catmull-Rom
        Vector3 p0 = splinePoints[Mathf.Max(0, index - 1)];
        Vector3 p1 = splinePoints[index];
        Vector3 p2 = splinePoints[Mathf.Min(splinePoints.Count - 1, index + 1)];
        Vector3 p3 = splinePoints[Mathf.Min(splinePoints.Count - 1, index + 2)];

        // Catmull-Rom interpolation
        return CatmullRom(p0, p1, p2, p3, localT);
    }

    Vector3 CatmullRom(Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3, float t)
    {
        float t2 = t * t;
        float t3 = t2 * t;

        return 0.5f * (
            (2f * p1) +
            (-p0 + p2) * t +
            (2f * p0 - 5f * p1 + 4f * p2 - p3) * t2 +
            (-p0 + 3f * p1 - 3f * p2 + p3) * t3
        );
    }

    Vector3 SnapToGround(Vector3 position)
    {
        Ray ray = new Ray(position + Vector3.up * 10f, Vector3.down);
        if (Physics.Raycast(ray, out RaycastHit hit, 20f, painter.groundLayer))
        {
            return hit.point;
        }
        return position;
    }

    void PlacePrefabsAlongSpline()
    {
        if (splinePositions.Count == 0) return;

        GameObject prefab = GetSelectedPrefab();
        if (prefab == null)
        {
            Debug.LogWarning("No prefab selected for spline placement");
            return;
        }

        // Create parent object for organization
        GameObject parent = new GameObject($"Spline_{prefab.name}");
        Undo.RegisterCreatedObjectUndo(parent, "Create Spline");

        for (int i = 0; i < splinePositions.Count; i++)
        {
            Vector3 position = splinePositions[i];
            Quaternion rotation = Quaternion.identity;

            // Auto-rotate to follow path direction
            if (painter.autoRotateToPath && i < splinePositions.Count - 1)
            {
                Vector3 direction = (splinePositions[i + 1] - position).normalized;
                if (direction != Vector3.zero)
                {
                    rotation = Quaternion.LookRotation(direction);
                }
            }

            // Apply base rotation
            if (painter.enableRotation)
            {
                rotation *= Quaternion.Euler(painter.baseRotation);
            }

            // Instantiate prefab
            GameObject instance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
            instance.transform.position = position;
            instance.transform.rotation = rotation;
            instance.transform.SetParent(parent.transform);

            // Register for undo
            Undo.RegisterCreatedObjectUndo(instance, "Place Spline Prefab");
        }

        Debug.Log($"Placed {splinePositions.Count} prefabs along spline");
    }

    void CompleteShape()
    {
        if (shapePositions.Count == 0)
        {
            Debug.LogWarning("No valid shape positions generated");
            return;
        }

        // Place prefabs in shape formation
        PlacePrefabsInShape();

        // Reset shape state
        CancelShape();
    }

    void CancelShape()
    {
        shapePositions.Clear();
        isCreatingShape = false;
        showShapePreview = false;
        SceneView.RepaintAll();
    }

    void GenerateShapePositions()
    {
        shapePositions.Clear();

        switch (painter.brushMode)
        {
            case BrushMode.Square:
                GenerateSquarePositions();
                break;
            case BrushMode.Rectangle:
                GenerateRectanglePositions();
                break;
            case BrushMode.Circle:
                GenerateCirclePositions();
                break;
        }
    }

    void GenerateSquarePositions()
    {
        float size = Vector3.Distance(shapeStartPos, shapeEndPos);
        if (size < 0.1f) return;

        Vector3 center = shapeCenter;
        float halfSize = size / 2f;

        if (painter.fillShape)
        {
            // Fill entire square
            int steps = Mathf.Max(1, Mathf.RoundToInt(size / painter.shapeSpacing));
            for (int x = 0; x <= steps; x++)
            {
                for (int z = 0; z <= steps; z++)
                {
                    float xPos = center.x - halfSize + (x * painter.shapeSpacing);
                    float zPos = center.z - halfSize + (z * painter.shapeSpacing);
                    Vector3 pos = new Vector3(xPos, center.y, zPos);

                    if (painter.snapShapeToGround)
                        pos = SnapToGround(pos);

                    shapePositions.Add(pos);
                }
            }
        }
        else
        {
            // Outline only
            int steps = Mathf.Max(1, Mathf.RoundToInt(size / painter.shapeSpacing));

            // Top and bottom edges
            for (int x = 0; x <= steps; x++)
            {
                float xPos = center.x - halfSize + (x * painter.shapeSpacing);

                // Top edge
                Vector3 topPos = new Vector3(xPos, center.y, center.z + halfSize);
                if (painter.snapShapeToGround) topPos = SnapToGround(topPos);
                shapePositions.Add(topPos);

                // Bottom edge
                Vector3 bottomPos = new Vector3(xPos, center.y, center.z - halfSize);
                if (painter.snapShapeToGround) bottomPos = SnapToGround(bottomPos);
                shapePositions.Add(bottomPos);
            }

            // Left and right edges (excluding corners to avoid duplicates)
            for (int z = 1; z < steps; z++)
            {
                float zPos = center.z - halfSize + (z * painter.shapeSpacing);

                // Left edge
                Vector3 leftPos = new Vector3(center.x - halfSize, center.y, zPos);
                if (painter.snapShapeToGround) leftPos = SnapToGround(leftPos);
                shapePositions.Add(leftPos);

                // Right edge
                Vector3 rightPos = new Vector3(center.x + halfSize, center.y, zPos);
                if (painter.snapShapeToGround) rightPos = SnapToGround(rightPos);
                shapePositions.Add(rightPos);
            }
        }
    }

    void GenerateRectanglePositions()
    {
        Vector3 size = shapeEndPos - shapeStartPos;
        float width = Mathf.Abs(size.x);
        float height = Mathf.Abs(size.z);

        if (width < 0.1f || height < 0.1f) return;

        Vector3 center = shapeCenter;
        float halfWidth = width / 2f;
        float halfHeight = height / 2f;

        if (painter.fillShape)
        {
            // Fill entire rectangle
            int stepsX = Mathf.Max(1, Mathf.RoundToInt(width / painter.shapeSpacing));
            int stepsZ = Mathf.Max(1, Mathf.RoundToInt(height / painter.shapeSpacing));

            for (int x = 0; x <= stepsX; x++)
            {
                for (int z = 0; z <= stepsZ; z++)
                {
                    float xPos = center.x - halfWidth + (x * painter.shapeSpacing);
                    float zPos = center.z - halfHeight + (z * painter.shapeSpacing);
                    Vector3 pos = new Vector3(xPos, center.y, zPos);

                    if (painter.snapShapeToGround)
                        pos = SnapToGround(pos);

                    shapePositions.Add(pos);
                }
            }
        }
        else
        {
            // Outline only
            int stepsX = Mathf.Max(1, Mathf.RoundToInt(width / painter.shapeSpacing));
            int stepsZ = Mathf.Max(1, Mathf.RoundToInt(height / painter.shapeSpacing));

            // Top and bottom edges
            for (int x = 0; x <= stepsX; x++)
            {
                float xPos = center.x - halfWidth + (x * painter.shapeSpacing);

                // Top edge
                Vector3 topPos = new Vector3(xPos, center.y, center.z + halfHeight);
                if (painter.snapShapeToGround) topPos = SnapToGround(topPos);
                shapePositions.Add(topPos);

                // Bottom edge
                Vector3 bottomPos = new Vector3(xPos, center.y, center.z - halfHeight);
                if (painter.snapShapeToGround) bottomPos = SnapToGround(bottomPos);
                shapePositions.Add(bottomPos);
            }

            // Left and right edges (excluding corners)
            for (int z = 1; z < stepsZ; z++)
            {
                float zPos = center.z - halfHeight + (z * painter.shapeSpacing);

                // Left edge
                Vector3 leftPos = new Vector3(center.x - halfWidth, center.y, zPos);
                if (painter.snapShapeToGround) leftPos = SnapToGround(leftPos);
                shapePositions.Add(leftPos);

                // Right edge
                Vector3 rightPos = new Vector3(center.x + halfWidth, center.y, zPos);
                if (painter.snapShapeToGround) rightPos = SnapToGround(rightPos);
                shapePositions.Add(rightPos);
            }
        }
    }

    void GenerateCirclePositions()
    {
        float radius = Vector3.Distance(shapeStartPos, shapeEndPos);
        if (radius < 0.1f) return;

        Vector3 center = shapeCenter;

        if (painter.fillShape)
        {
            // Fill entire circle
            int steps = Mathf.Max(1, Mathf.RoundToInt((radius * 2f) / painter.shapeSpacing));

            for (int x = 0; x <= steps; x++)
            {
                for (int z = 0; z <= steps; z++)
                {
                    float xPos = center.x - radius + (x * painter.shapeSpacing);
                    float zPos = center.z - radius + (z * painter.shapeSpacing);
                    Vector3 pos = new Vector3(xPos, center.y, zPos);

                    // Check if point is inside circle
                    float distance = Vector3.Distance(new Vector3(pos.x, center.y, pos.z), center);
                    if (distance <= radius)
                    {
                        if (painter.snapShapeToGround)
                            pos = SnapToGround(pos);

                        shapePositions.Add(pos);
                    }
                }
            }
        }
        else
        {
            // Outline only - create circle perimeter
            for (int i = 0; i < painter.circleSegments; i++)
            {
                float angle = (float)i / painter.circleSegments * 2f * Mathf.PI;
                float xPos = center.x + Mathf.Cos(angle) * radius;
                float zPos = center.z + Mathf.Sin(angle) * radius;
                Vector3 pos = new Vector3(xPos, center.y, zPos);

                if (painter.snapShapeToGround)
                    pos = SnapToGround(pos);

                shapePositions.Add(pos);
            }
        }
    }

    void PlacePrefabsInShape()
    {
        if (shapePositions.Count == 0) return;

        GameObject prefab = GetSelectedPrefab();
        if (prefab == null)
        {
            Debug.LogWarning("No prefab selected for shape placement");
            return;
        }

        // Create parent object for organization
        string shapeName = painter.brushMode.ToString();
        GameObject parent = new GameObject($"{shapeName}_{prefab.name}");
        Undo.RegisterCreatedObjectUndo(parent, $"Create {shapeName}");

        for (int i = 0; i < shapePositions.Count; i++)
        {
            Vector3 position = shapePositions[i];
            Quaternion rotation = Quaternion.identity;

            // Apply base rotation
            if (painter.enableRotation)
            {
                rotation = Quaternion.Euler(painter.baseRotation);

                // Apply random rotation if enabled
                if (painter.randomRotation)
                {
                    Vector3 randomRot = new Vector3(
                        Random.Range(-painter.randomRotationRange.x, painter.randomRotationRange.x),
                        Random.Range(-painter.randomRotationRange.y, painter.randomRotationRange.y),
                        Random.Range(-painter.randomRotationRange.z, painter.randomRotationRange.z)
                    );
                    rotation *= Quaternion.Euler(randomRot);
                }
            }

            // Instantiate prefab
            GameObject instance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
            instance.transform.position = position;
            instance.transform.rotation = rotation;
            instance.transform.SetParent(parent.transform);

            // Register for undo
            Undo.RegisterCreatedObjectUndo(instance, $"Place {shapeName} Prefab");
        }

        Debug.Log($"Placed {shapePositions.Count} prefabs in {painter.brushMode} formation");
    }

    void DrawShapePreview()
    {
        if (!painter.showShapePreview) return;

        Handles.color = painter.shapePreviewColor;

        // Draw current mouse position
        if (showShapePreview)
        {
            Handles.SphereHandleCap(0, currentShapePos, Quaternion.identity, 0.1f, EventType.Repaint);
        }

        // Draw shape being created
        if (isCreatingShape)
        {
            switch (painter.brushMode)
            {
                case BrushMode.Square:
                    DrawSquarePreview();
                    break;
                case BrushMode.Rectangle:
                    DrawRectanglePreview();
                    break;
                case BrushMode.Circle:
                    DrawCirclePreview();
                    break;
            }

            // Draw placement positions
            DrawShapePlacementPreview();
        }
    }

    void DrawSquarePreview()
    {
        float size = Vector3.Distance(shapeStartPos, shapeEndPos);
        Vector3 center = shapeCenter;
        float halfSize = size / 2f;

        // Draw square outline
        Vector3[] corners = new Vector3[4]
        {
            new Vector3(center.x - halfSize, center.y, center.z - halfSize),
            new Vector3(center.x + halfSize, center.y, center.z - halfSize),
            new Vector3(center.x + halfSize, center.y, center.z + halfSize),
            new Vector3(center.x - halfSize, center.y, center.z + halfSize)
        };

        Handles.DrawPolyLine(corners[0], corners[1], corners[2], corners[3], corners[0]);
    }

    void DrawRectanglePreview()
    {
        Vector3 size = shapeEndPos - shapeStartPos;
        Vector3 center = shapeCenter;
        float halfWidth = Mathf.Abs(size.x) / 2f;
        float halfHeight = Mathf.Abs(size.z) / 2f;

        // Draw rectangle outline
        Vector3[] corners = new Vector3[4]
        {
            new Vector3(center.x - halfWidth, center.y, center.z - halfHeight),
            new Vector3(center.x + halfWidth, center.y, center.z - halfHeight),
            new Vector3(center.x + halfWidth, center.y, center.z + halfHeight),
            new Vector3(center.x - halfWidth, center.y, center.z + halfHeight)
        };

        Handles.DrawPolyLine(corners[0], corners[1], corners[2], corners[3], corners[0]);
    }

    void DrawCirclePreview()
    {
        float radius = Vector3.Distance(shapeStartPos, shapeEndPos);
        Vector3 center = shapeCenter;

        // Draw circle outline
        Handles.DrawWireDisc(center, Vector3.up, radius);
    }

    void DrawShapePlacementPreview()
    {
        if (shapePositions.Count == 0) return;

        Handles.color = Color.green;

        // Draw placement positions
        foreach (Vector3 pos in shapePositions)
        {
            Handles.CubeHandleCap(0, pos, Quaternion.identity, 0.1f, EventType.Repaint);
        }
    }

    void DrawSplinePreview()
    {
        if (!painter.showSplinePreview) return;

        Handles.color = painter.splineColor;

        // Draw existing spline points
        for (int i = 0; i < splinePoints.Count; i++)
        {
            // Draw point
            Handles.SphereHandleCap(0, splinePoints[i], Quaternion.identity, 0.2f, EventType.Repaint);

            // Draw line to next point
            if (i < splinePoints.Count - 1)
            {
                Handles.DrawLine(splinePoints[i], splinePoints[i + 1]);
            }
        }

        // Draw line from last point to current mouse position
        if (isCreatingSpline && splinePoints.Count > 0 && showSplinePreview)
        {
            Handles.color = painter.splineColor * 0.7f;
            Handles.DrawDottedLine(splinePoints[splinePoints.Count - 1], currentSplinePoint, 5f);
        }

        // Draw current point preview
        if (showSplinePreview)
        {
            Handles.color = painter.splineColor * 0.8f;
            Handles.SphereHandleCap(0, currentSplinePoint, Quaternion.identity, 0.15f, EventType.Repaint);
        }

        // Draw spline curve if we have enough points
        if (splinePoints.Count >= 2)
        {
            DrawSplineCurve();
        }

        // Draw placement positions if spline is being created
        if (isCreatingSpline && splinePoints.Count >= 2)
        {
            GenerateSplinePositions();
            DrawPlacementPreview();
        }
    }

    void DrawSplineCurve()
    {
        if (splinePoints.Count < 2) return;

        Handles.color = painter.splineColor;

        // Draw smooth curve
        int resolution = painter.splineResolution * splinePoints.Count;
        Vector3 lastPos = splinePoints[0];

        for (int i = 1; i <= resolution; i++)
        {
            float t = (float)i / resolution;
            Vector3 currentPos = GetSplinePosition(t);

            Handles.DrawLine(lastPos, currentPos);
            lastPos = currentPos;
        }
    }

    void DrawPlacementPreview()
    {
        if (splinePositions.Count == 0) return;

        Handles.color = Color.green;

        // Draw placement positions
        foreach (Vector3 pos in splinePositions)
        {
            Handles.CubeHandleCap(0, pos, Quaternion.identity, 0.1f, EventType.Repaint);
        }

        // Draw spacing indicators
        Handles.color = Color.yellow;
        for (int i = 0; i < splinePositions.Count - 1; i++)
        {
            Vector3 midPoint = (splinePositions[i] + splinePositions[i + 1]) / 2f;
            Handles.DrawWireDisc(midPoint, Vector3.up, 0.05f);
        }
    }

    void CalculateInitialGridSize()
    {
        // Start with a minimal grid at the click point
        gridCenter = gridStartPos;
        gridSize = new Vector2(painter.gridSize * 0.5f, painter.gridSize * 0.5f);
        CalculateGridSize();
    }

    void UpdateGridFromDrag()
    {
        gridCenter = (gridStartPos + gridEndPos) / 2f;
        Vector3 diff = gridEndPos - gridStartPos;
        gridSize = new Vector2(Mathf.Max(0.5f, Mathf.Abs(diff.x)), Mathf.Max(0.5f, Mathf.Abs(diff.z)));
        CalculateGridSize();
    }

    void CalculateGridSize()
    {
        float cellSize = painter.gridSize;

        // Use custom dimensions if enabled, otherwise calculate from grid size
        if (painter.useCustomDimensions)
        {
            rows = painter.customRows;
            columns = painter.customColumns;
        }
        else
        {
            // Calculate rows and columns based on current grid size
            columns = Mathf.Max(1, Mathf.RoundToInt(gridSize.x / cellSize));
            rows = Mathf.Max(1, Mathf.RoundToInt(gridSize.y / cellSize));

            // Ensure at least 1x1 grid
            if (columns == 0) columns = 1;
            if (rows == 0) rows = 1;
        }

        // Generate grid positions evenly distributed within the grid bounds
        gridPositions.Clear();

        Vector3 startPos = new(
            gridCenter.x - (gridSize.x / 2f),
            gridCenter.y,
            gridCenter.z - (gridSize.y / 2f)
        );

        Vector3 endPos = new(
            gridCenter.x + (gridSize.x / 2f),
            gridCenter.y,
            gridCenter.z + (gridSize.y / 2f)
        );

        for (int row = 0; row < rows; row++)
        {
            for (int col = 0; col < columns; col++)
            {
                float xProgress = columns > 1 ? (float)col / (columns - 1) : 0.5f;
                float zProgress = rows > 1 ? (float)row / (rows - 1) : 0.5f;

                Vector3 pos = new(
                    Mathf.Lerp(startPos.x, endPos.x, xProgress),
                    startPos.y,
                    Mathf.Lerp(startPos.z, endPos.z, zProgress)
                );

                gridPositions.Add(pos);
            }
        }
    }
    
    void DrawGridPreview()
    {
        if (gridPositions.Count == 0) return;
        
        Handles.color = painter.gridColor;
        
        // Draw grid blocks
        foreach (Vector3 pos in gridPositions)
        {
            Vector3 cubeSize = new(painter.gridSize * 0.9f, 0.1f, painter.gridSize * 0.9f);
            Handles.DrawWireCube(pos, cubeSize);
        }
        
        // Draw grid outline
        Vector3 center = gridCenter;
        Vector3 size = new(
            gridSize.x,
            0.1f,
            gridSize.y
        );
        
        Handles.color = Color.yellow;
        Handles.DrawWireCube(center, size);
        
        // Draw info labels
        Vector2 infoScreenPos = GetWorldToScreenPoint(gridStartPos);
        if (infoScreenPos.x > 0 && infoScreenPos.y > 0)
        {
            Handles.BeginGUI();
            int yOffset = -80;
            GUI.Label(new Rect(infoScreenPos.x, infoScreenPos.y + yOffset, 200, 20), $"Grid: {rows} x {columns}");
            yOffset += 20;
            GUI.Label(new Rect(infoScreenPos.x, infoScreenPos.y + yOffset, 200, 20), $"Cells: {gridPositions.Count}");
            yOffset += 20;
            GUI.Label(new Rect(infoScreenPos.x, infoScreenPos.y + yOffset, 200, 20), $"Cell Size: {painter.gridSize:F1}");

            if (painter.enableRotation)
            {
                yOffset += 20;
                GUI.Label(new Rect(infoScreenPos.x, infoScreenPos.y + yOffset, 200, 20), $"Rotation: {painter.baseRotation.y:F0}°");
                yOffset += 20;
                GUI.Label(new Rect(infoScreenPos.x, infoScreenPos.y + yOffset, 200, 20), "R/T: Rotate, Y: Reset");
            }

            if (painter.enableGridResize)
            {
                yOffset += 20;
                GUI.Label(new Rect(infoScreenPos.x, infoScreenPos.y + yOffset, 200, 20), $"Size: {gridSize.x:F1} x {gridSize.y:F1}");
                if (isResizingGrid)
                {
                    yOffset += 20;
                    GUI.Label(new Rect(infoScreenPos.x, infoScreenPos.y + yOffset, 200, 20), "🔄 Resizing...");
                }
                else
                {
                    yOffset += 20;
                    GUI.Label(new Rect(infoScreenPos.x, infoScreenPos.y + yOffset, 200, 20), "Drag yellow handles");
                }
            }

            Handles.EndGUI();
        }
    }
    
    void PlacePrefabsInGrid()
    {
        if (painter.prefabsToPlace == null || painter.prefabsToPlace.Length == 0)
        {
            Debug.LogWarning("No prefabs assigned to place!");
            return;
        }

        // Get valid prefabs
        var validPrefabs = new List<GameObject>();
        foreach (var prefab in painter.prefabsToPlace)
        {
            if (prefab != null) validPrefabs.Add(prefab);
        }

        if (validPrefabs.Count == 0)
        {
            Debug.LogWarning("No valid prefabs found!");
            return;
        }

        // Create parent object for organization
        string parentName = painter.placeMultiplePrefabs ? "Grid_Multiple" : $"Grid_{validPrefabs[painter.selectedPrefabIndex].name}";
        GameObject gridParent = new($"{parentName}_{System.DateTime.Now:HHmmss}");
        gridParent.transform.SetParent(painter.transform);

        int placedCount = 0;

        foreach (Vector3 pos in gridPositions)
        {
            // Raycast to get exact ground position
            Ray ray = new(pos + Vector3.up * 10f, Vector3.down);

            Vector3 placePos = pos;
            if (Physics.Raycast(ray, out RaycastHit hit, 20f, painter.groundLayer))
            {
                placePos = hit.point;
            }

            // Select prefab to place
            GameObject prefabToPlace = GetPrefabToPlace(validPrefabs, placedCount);

            if (painter.placeMultiplePrefabs && painter.randomPrefabSelection)
            {
                // Place multiple random prefabs at this position
                int numToPlace = Random.Range(1, Mathf.Min(4, validPrefabs.Count + 1));
                for (int i = 0; i < numToPlace; i++)
                {
                    GameObject randomPrefab = validPrefabs[Random.Range(0, validPrefabs.Count)];
                    Vector3 offsetPos = placePos + new Vector3(
                        Random.Range(-0.3f, 0.3f),
                        0,
                        Random.Range(-0.3f, 0.3f)
                    );

                    GameObject newObj = PrefabUtility.InstantiatePrefab(randomPrefab) as GameObject;
                    newObj.transform.position = offsetPos;

                    // Apply rotation
                    ApplyRotation(newObj, placedCount);

                    newObj.transform.SetParent(gridParent.transform);

                    // Register undo
                    Undo.RegisterCreatedObjectUndo(newObj, "Place Prefab in Grid");
                    placedCount++;
                }
            }
            else
            {
                // Place single prefab
                GameObject newObj = PrefabUtility.InstantiatePrefab(prefabToPlace) as GameObject;
                newObj.transform.position = placePos;

                // Apply rotation
                ApplyRotation(newObj, placedCount);

                newObj.transform.SetParent(gridParent.transform);

                // Register undo
                Undo.RegisterCreatedObjectUndo(newObj, "Place Prefab in Grid");
                placedCount++;
            }
        }

        // Register undo for parent
        Undo.RegisterCreatedObjectUndo(gridParent, "Create Grid Parent");

        Debug.Log($"Placed {placedCount} prefabs in {rows}x{columns} grid");

        // Clear grid data
        gridPositions.Clear();
    }

    GameObject GetPrefabToPlace(List<GameObject> validPrefabs, int index)
    {
        if (painter.placeMultiplePrefabs)
        {
            if (painter.randomPrefabSelection)
            {
                return validPrefabs[Random.Range(0, validPrefabs.Count)];
            }
            else
            {
                // Cycle through prefabs
                return validPrefabs[index % validPrefabs.Count];
            }
        }
        else
        {
            // Use selected prefab
            int selectedIndex = Mathf.Clamp(painter.selectedPrefabIndex, 0, validPrefabs.Count - 1);
            return validPrefabs[selectedIndex];
        }
    }

    void ApplyRotation(GameObject obj, int index)
    {
        if (!painter.enableRotation) return;

        Vector3 finalRotation = painter.baseRotation;

        if (painter.randomRotation)
        {
            // Apply random rotation within specified range
            finalRotation += new Vector3(
                Random.Range(-painter.randomRotationRange.x / 2f, painter.randomRotationRange.x / 2f),
                Random.Range(-painter.randomRotationRange.y / 2f, painter.randomRotationRange.y / 2f),
                Random.Range(-painter.randomRotationRange.z / 2f, painter.randomRotationRange.z / 2f)
            );
        }
        else
        {
            // Apply stepped rotation based on index
            finalRotation.y += (index * painter.rotationStep) % 360f;
        }

        obj.transform.rotation = Quaternion.Euler(finalRotation);
    }



    ResizeHandle GetResizeHandleAtPosition(Vector2 mousePos)
    {
        float handleSize = 20f; // Larger for easier clicking

        // Get handle positions in screen space
        Vector2 topLeft = GetWorldToScreenPoint(gridCenter + new Vector3(-gridSize.x/2, 0, gridSize.y/2));
        Vector2 topRight = GetWorldToScreenPoint(gridCenter + new Vector3(gridSize.x/2, 0, gridSize.y/2));
        Vector2 bottomLeft = GetWorldToScreenPoint(gridCenter + new Vector3(-gridSize.x/2, 0, -gridSize.y/2));
        Vector2 bottomRight = GetWorldToScreenPoint(gridCenter + new Vector3(gridSize.x/2, 0, -gridSize.y/2));

        Vector2 top = GetWorldToScreenPoint(gridCenter + new Vector3(0, 0, gridSize.y/2));
        Vector2 bottom = GetWorldToScreenPoint(gridCenter + new Vector3(0, 0, -gridSize.y/2));
        Vector2 left = GetWorldToScreenPoint(gridCenter + new Vector3(-gridSize.x/2, 0, 0));
        Vector2 right = GetWorldToScreenPoint(gridCenter + new Vector3(gridSize.x/2, 0, 0));

        // Check corner handles first (priority)
        if (Vector2.Distance(mousePos, topLeft) < handleSize) return ResizeHandle.TopLeft;
        if (Vector2.Distance(mousePos, topRight) < handleSize) return ResizeHandle.TopRight;
        if (Vector2.Distance(mousePos, bottomLeft) < handleSize) return ResizeHandle.BottomLeft;
        if (Vector2.Distance(mousePos, bottomRight) < handleSize) return ResizeHandle.BottomRight;

        // Check edge handles
        if (Vector2.Distance(mousePos, top) < handleSize) return ResizeHandle.Top;
        if (Vector2.Distance(mousePos, bottom) < handleSize) return ResizeHandle.Bottom;
        if (Vector2.Distance(mousePos, left) < handleSize) return ResizeHandle.Left;
        if (Vector2.Distance(mousePos, right) < handleSize) return ResizeHandle.Right;

        return ResizeHandle.None;
    }

    void SetCursorForHandle(ResizeHandle handle)
    {
        MouseCursor cursor = MouseCursor.Arrow;

        switch (handle)
        {
            case ResizeHandle.TopLeft:
            case ResizeHandle.BottomRight:
                cursor = MouseCursor.ResizeUpLeft;
                break;
            case ResizeHandle.TopRight:
            case ResizeHandle.BottomLeft:
                cursor = MouseCursor.ResizeUpRight;
                break;
            case ResizeHandle.Top:
            case ResizeHandle.Bottom:
                cursor = MouseCursor.ResizeVertical;
                break;
            case ResizeHandle.Left:
            case ResizeHandle.Right:
                cursor = MouseCursor.ResizeHorizontal;
                break;
        }

        EditorGUIUtility.AddCursorRect(new Rect(0, 0, Screen.width, Screen.height), cursor);
    }

    GameObject GetSelectedPrefab()
    {
        if (painter.prefabsToPlace == null || painter.prefabsToPlace.Length == 0)
            return null;

        // Get valid prefabs (non-null)
        List<GameObject> validPrefabs = new();
        foreach (GameObject prefab in painter.prefabsToPlace)
        {
            if (prefab != null) validPrefabs.Add(prefab);
        }

        if (validPrefabs.Count == 0) return null;

        // Return selected prefab or random if random selection is enabled
        if (painter.randomPrefabSelection)
        {
            return validPrefabs[Random.Range(0, validPrefabs.Count)];
        }
        else
        {
            int selectedIndex = Mathf.Clamp(painter.selectedPrefabIndex, 0, validPrefabs.Count - 1);
            return validPrefabs[selectedIndex];
        }
    }

    void PlaceSinglePrefab(Vector3 position)
    {
        GameObject prefabToPlace = GetSelectedPrefab();
        if (prefabToPlace == null) return;

        // Create the prefab instance
        GameObject instance = PrefabUtility.InstantiatePrefab(prefabToPlace) as GameObject;
        if (instance != null)
        {
            // Set parent first (this is important for proper positioning)
            instance.transform.SetParent(painter.transform);

            // Center the prefab at the position
            // Get the bounds to properly center it
            Renderer renderer = instance.GetComponent<Renderer>();
            if (renderer != null)
            {
                // Position based on bounds center
                Vector3 boundsCenter = renderer.bounds.center;
                Vector3 offset = instance.transform.position - boundsCenter;
                instance.transform.position = position + offset;
            }
            else
            {
                // Fallback to direct positioning
                instance.transform.position = position;
            }

            // Apply rotation
            ApplyRotation(instance, 0);

            // Register undo
            Undo.RegisterCreatedObjectUndo(instance, "Place Single Prefab");
        }
    }

    void DrawSingleBrushPreview()
    {
        if (!painter.enableBrushPreview) return;

        // Change color based on whether we're actively placing
        Color previewColor = isContinuousPlacing ? Color.green : painter.brushPreviewColor;
        Handles.color = previewColor;

        // Draw preview circle
        Handles.DrawWireDisc(singleBrushPosition, Vector3.up, painter.brushPreviewSize);

        // Draw crosshair at center
        float crossSize = painter.brushPreviewSize * 0.3f;
        Handles.DrawLine(
            singleBrushPosition + Vector3.forward * crossSize,
            singleBrushPosition + Vector3.back * crossSize
        );
        Handles.DrawLine(
            singleBrushPosition + Vector3.right * crossSize,
            singleBrushPosition + Vector3.left * crossSize
        );

        // Draw preview prefab outline
        GameObject prefab = GetSelectedPrefab();
        if (prefab != null)
        {
            // Draw a simple representation of the prefab
            Handles.color = previewColor * 0.7f;
            Handles.DrawWireCube(singleBrushPosition, Vector3.one * painter.brushPreviewSize * 0.8f);

            // Draw prefab name with status
            string statusText = isContinuousPlacing ? $"{prefab.name} (Placing...)" : prefab.name;
            Handles.Label(singleBrushPosition + Vector3.up * 2f, statusText);
        }

        // Show placement instructions
        if (!isContinuousPlacing)
        {
            Handles.color = Color.white;
            Handles.Label(singleBrushPosition + Vector3.up * 3f, "Shift + Click/Drag to place");
        }
    }

    bool CanPlacePhysicsPrefab()
    {
        GameObject prefab = GetSelectedPrefab();
        if (prefab == null) return false;

        bool hasCollider = false;
        bool hasRigidbody = false;

        // Check for collider
        if (painter.requireCollider)
        {
            hasCollider = prefab.GetComponent<Collider>() != null ||
                         prefab.GetComponentInChildren<Collider>() != null;
        }
        else
        {
            hasCollider = true; // Don't require if not needed
        }

        // Check for rigidbody
        if (painter.requireRigidbody)
        {
            hasRigidbody = prefab.GetComponent<Rigidbody>() != null ||
                          prefab.GetComponentInChildren<Rigidbody>() != null;
        }
        else
        {
            hasRigidbody = true; // Don't require if not needed
        }

        return hasCollider && hasRigidbody;
    }

    void PlacePhysicsPrefab(Vector3 dropPosition, Vector3 targetPosition)
    {
        GameObject prefabToPlace = GetSelectedPrefab();
        if (prefabToPlace == null || !CanPlacePhysicsPrefab()) return;

        // Create the prefab instance at drop height
        GameObject instance = PrefabUtility.InstantiatePrefab(prefabToPlace) as GameObject;
        if (instance != null)
        {
            // Set parent first
            instance.transform.SetParent(painter.transform);

            // Position at drop height
            instance.transform.position = dropPosition;

            // Apply rotation
            ApplyRotation(instance, 0);

            // Ensure rigidbody exists and is configured for physics
            Rigidbody rb = instance.GetComponent<Rigidbody>();
            if (rb == null)
            {
                rb = instance.GetComponentInChildren<Rigidbody>();
            }

            if (rb != null && painter.enablePhysicsSimulation)
            {
                // Enable physics simulation in editor
                rb.isKinematic = false;
                rb.useGravity = true;

                // Set consistent physics properties based on inspector settings
                if (painter.varyMass)
                {
                    // Use position-based seed for consistent randomness
                    Random.InitState((int)(instance.transform.position.x * 1000 + instance.transform.position.z * 1000));
                    rb.mass = Random.Range(painter.minMass, painter.maxMass);
                }
                else
                {
                    rb.mass = (painter.minMass + painter.maxMass) / 2f; // Use average mass
                }

                // Set consistent damping values
                rb.linearDamping = 0.2f; // Fixed value for consistency
                rb.angularDamping = 0.75f; // Fixed value for consistency

                // Add controlled forces based on inspector settings
                Random.InitState((int)(instance.transform.position.x * 1000 + instance.transform.position.z * 1000));

                Vector3 controlledForce = new Vector3(
                    Random.Range(-painter.forceIntensity, painter.forceIntensity) * 0.5f,
                    Random.Range(-painter.forceIntensity * 0.25f, painter.forceIntensity * 0.25f),
                    Random.Range(-painter.forceIntensity, painter.forceIntensity) * 0.5f
                );

                Vector3 controlledTorque = new Vector3(
                    Random.Range(-painter.torqueIntensity, painter.torqueIntensity) * 0.5f,
                    Random.Range(-painter.torqueIntensity, painter.torqueIntensity) * 0.5f,
                    Random.Range(-painter.torqueIntensity, painter.torqueIntensity) * 0.5f
                );

                rb.AddForce(controlledForce, ForceMode.Impulse);
                rb.AddTorque(controlledTorque, ForceMode.Impulse);

                // Add consistent wind effect
                if (painter.addWindEffect)
                {
                    Vector3 windForce = painter.windDirection.normalized * painter.windStrength;
                    // Add small controlled variation based on position
                    windForce += new Vector3(
                        Mathf.Sin(instance.transform.position.x) * 0.3f,
                        Mathf.Sin(instance.transform.position.y) * 0.1f,
                        Mathf.Cos(instance.transform.position.z) * 0.3f
                    );
                    rb.AddForce(windForce, ForceMode.Force);
                }

                // Add controlled tumble effect
                if (painter.addTumbleEffect)
                {
                    // Use position-based deterministic "randomness"
                    float tumbleX = Mathf.Sin(instance.transform.position.x * 2f) * painter.tumbleChance * 2f;
                    float tumbleY = Mathf.Abs(Mathf.Sin(instance.transform.position.z * 1.5f)) * painter.tumbleChance * 1.5f + 0.5f;
                    float tumbleZ = Mathf.Cos(instance.transform.position.x * 1.8f) * painter.tumbleChance * 2f;

                    Vector3 tumbleForce = new Vector3(tumbleX, tumbleY, tumbleZ);
                    rb.AddForce(tumbleForce, ForceMode.Impulse);
                }

                // Add bouncy physics material if enabled
                if (painter.addRandomBounce)
                {
                    AddBouncyMaterial(instance, painter.bounciness);
                }

                // Add to simulation list for tracking
                simulatingObjects.Add(instance);

                // Start editor physics simulation immediately
                StartEditorPhysicsSimulation(instance, rb);
            }

            // Register undo
            Undo.RegisterCreatedObjectUndo(instance, "Place Physics Prefab");
        }
    }

    void StartEditorPhysicsSimulation(GameObject obj, Rigidbody rb)
    {
        if (obj == null || rb == null) return;

        // Store original kinematic state of ALL rigidbodies to restore later
        StoreOriginalPhysicsStates();

        // Set ONLY the newly placed object to simulate
        SetupNewObjectForSimulation(obj, rb);

        // Ensure physics simulation is properly initialized
        if (Physics.simulationMode != SimulationMode.Script)
        {
            Physics.simulationMode = SimulationMode.Script;
            Debug.Log("Physics simulation mode set to Script for consistent behavior");
        }

        // Store initial physics settings for consistency
        float startTime = Time.realtimeSinceStartup;
        int frameCount = 0;

        // Ensure consistent physics timestep
        float fixedTimeStep = 0.02f; // 50 FPS for consistent simulation

        // Create and store callback for proper cleanup
        EditorApplication.CallbackFunction callback = () => UpdateEditorPhysicsSimulation(obj, rb, startTime, frameCount, fixedTimeStep);
        simulationCallbacks[obj] = callback;
        EditorApplication.update += callback;

        Debug.Log($"Started physics simulation for {obj.name} at position {obj.transform.position}");
    }

    void StoreOriginalPhysicsStates()
    {
        // Only store states once per simulation session
        if (originalKinematicStates.Count > 0) return;

        // Find all rigidbodies in the scene
        Rigidbody[] allRigidbodies = FindObjectsByType<Rigidbody>(FindObjectsSortMode.None);

        foreach (Rigidbody rb in allRigidbodies)
        {
            // Store original states
            originalKinematicStates[rb] = rb.isKinematic;
            originalGravityStates[rb] = rb.useGravity;

            // Set existing objects to kinematic to prevent them from simulating
            if (!simulatingObjects.Contains(rb.gameObject))
            {
                rb.isKinematic = true;
                rb.useGravity = false;
            }
        }

        Debug.Log($"Stored physics states for {allRigidbodies.Length} existing rigidbodies");
    }

    void SetupNewObjectForSimulation(GameObject obj, Rigidbody rb)
    {
        // This object should simulate - make sure it's not kinematic
        rb.isKinematic = false;
        rb.useGravity = true;

        // Store its original state too (it should be non-kinematic)
        originalKinematicStates[rb] = false;
        originalGravityStates[rb] = true;
    }

    void RestoreOriginalPhysicsStates()
    {
        // Restore all rigidbodies to their original states
        foreach (var kvp in originalKinematicStates)
        {
            Rigidbody rb = kvp.Key;
            if (rb != null)
            {
                rb.isKinematic = kvp.Value;
                if (originalGravityStates.ContainsKey(rb))
                {
                    rb.useGravity = originalGravityStates[rb];
                }
            }
        }

        // Clear stored states
        originalKinematicStates.Clear();
        originalGravityStates.Clear();

        Debug.Log("Restored original physics states for all rigidbodies");
    }

    void UpdateEditorPhysicsSimulation(GameObject obj, Rigidbody rb, float startTime, int frameCount, float fixedTimeStep)
    {
        if (obj == null || rb == null)
        {
            // Clean up callback properly
            if (simulationCallbacks.ContainsKey(obj))
            {
                EditorApplication.update -= simulationCallbacks[obj];
                simulationCallbacks.Remove(obj);
            }
            return;
        }

        // Apply continuous wind force during simulation
        if (painter.addWindEffect && rb != null)
        {
            Vector3 continuousWind = painter.windDirection.normalized * (painter.windStrength * 0.1f);
            rb.AddForce(continuousWind, ForceMode.Force);
        }

        // Simulate physics step by step with consistent timestep
        Physics.Simulate(fixedTimeStep);
        frameCount++;

        float elapsed = Time.realtimeSinceStartup - startTime;

        // Check if object has settled with consistent thresholds
        bool hasSettled = rb.linearVelocity.magnitude < 0.1f && rb.angularVelocity.magnitude < 0.1f;
        bool timeExpired = elapsed >= painter.simulationTime;
        bool minFramesPassed = frameCount > 60; // Minimum frames for settling detection

        // Stop simulation based on consistent criteria
        if ((hasSettled && minFramesPassed) || (painter.autoStopPhysics && timeExpired))
        {
            // Stop simulation
            if (rb != null && painter.autoStopPhysics)
            {
                rb.isKinematic = true;
                rb.linearVelocity = Vector3.zero;
                rb.angularVelocity = Vector3.zero;
            }

            // Remove from simulation list
            simulatingObjects.Remove(obj);

            // Remove update callback properly
            if (simulationCallbacks.ContainsKey(obj))
            {
                EditorApplication.update -= simulationCallbacks[obj];
                simulationCallbacks.Remove(obj);
                Debug.Log($"Stopped physics simulation for {obj.name}");
            }

            // Re-enable auto simulation if no more objects simulating
            if (simulatingObjects.Count == 0)
            {
                Physics.simulationMode = SimulationMode.FixedUpdate;
            }

            // Mark scene as dirty for saving
            EditorUtility.SetDirty(obj);
            SceneView.RepaintAll();
        }
    }

    void StopAllPhysicsSimulations()
    {
        // Stop all ongoing simulations
        GameObject[] objectsToStop = new GameObject[simulatingObjects.Count];
        simulatingObjects.CopyTo(objectsToStop);

        foreach (GameObject obj in objectsToStop)
        {
            if (obj != null)
            {
                Rigidbody rb = obj.GetComponent<Rigidbody>();
                if (rb != null && painter.autoStopPhysics)
                {
                    rb.isKinematic = true;
                    rb.linearVelocity = Vector3.zero;
                    rb.angularVelocity = Vector3.zero;
                }
                EditorUtility.SetDirty(obj);

                // Remove callback for this object
                if (simulationCallbacks.ContainsKey(obj))
                {
                    EditorApplication.update -= simulationCallbacks[obj];
                    simulationCallbacks.Remove(obj);
                }
            }
        }

        simulatingObjects.Clear();
        simulationCallbacks.Clear();

        // Restore original physics states for all objects
        RestoreOriginalPhysicsStates();

        Physics.simulationMode = SimulationMode.FixedUpdate;
        SceneView.RepaintAll();

        Debug.Log("All physics simulations stopped and original states restored");
    }

    void ResetPhysicsSimulation()
    {
        // Clear any existing simulations
        if (simulatingObjects.Count > 0)
        {
            StopAllPhysicsSimulations();
        }

        // Clean up any remaining callbacks
        foreach (var callback in simulationCallbacks.Values)
        {
            EditorApplication.update -= callback;
        }

        // Reset physics simulation mode to default
        Physics.simulationMode = SimulationMode.FixedUpdate;

        // Clear all lists
        simulatingObjects.Clear();
        simulationCallbacks.Clear();

        // Restore original physics states
        RestoreOriginalPhysicsStates();

        Debug.Log("Physics simulation state completely reset - ready for consistent behavior");
    }

    void AddBouncyMaterial(GameObject obj, float bounciness)
    {
        // Create a consistent bouncy physics material
        PhysicsMaterial bouncyMaterial = new PhysicsMaterial("BouncyMaterial");
        bouncyMaterial.bounciness = bounciness;

        // Use consistent friction values based on bounciness
        float frictionValue = Mathf.Lerp(0.4f, 0.1f, bounciness); // Less friction = more bouncy
        bouncyMaterial.dynamicFriction = frictionValue;
        bouncyMaterial.staticFriction = frictionValue;
        bouncyMaterial.frictionCombine = PhysicsMaterialCombine.Average;
        bouncyMaterial.bounceCombine = PhysicsMaterialCombine.Maximum;

        // Apply to all colliders on the object
        Collider[] colliders = obj.GetComponentsInChildren<Collider>();
        foreach (Collider col in colliders)
        {
            col.material = bouncyMaterial;
        }
    }

    void DrawPhysicsBrushPreview()
    {
        if (!painter.showDropPreview) return;

        // Check if prefab can be placed
        bool canPlace = CanPlacePhysicsPrefab();
        Color previewColor = canPlace ? painter.physicsPreviewColor : Color.red;

        // Change color if actively placing
        if (isPhysicsPlacing && canPlace)
        {
            previewColor = Color.green;
        }

        Handles.color = previewColor;

        // Draw drop position (where prefab spawns)
        Handles.DrawWireDisc(physicsDropPosition, Vector3.up, painter.brushPreviewSize);
        Handles.DrawLine(physicsDropPosition, physicsBrushPosition);

        // Draw target position (where it should land)
        Handles.color = previewColor * 0.7f;
        Handles.DrawWireDisc(physicsBrushPosition, Vector3.up, painter.brushPreviewSize * 0.8f);

        // Draw drop height indicator
        Handles.color = previewColor * 0.5f;
        Handles.DrawLine(
            physicsBrushPosition + Vector3.up * 0.1f,
            physicsDropPosition + Vector3.down * 0.1f
        );

        // Draw prefab info
        GameObject prefab = GetSelectedPrefab();
        if (prefab != null)
        {
            string statusText = "";
            if (!canPlace)
            {
                statusText = $"{prefab.name} (Missing ";
                if (painter.requireCollider && prefab.GetComponent<Collider>() == null)
                    statusText += "Collider ";
                if (painter.requireRigidbody && prefab.GetComponent<Rigidbody>() == null)
                    statusText += "Rigidbody ";
                statusText += ")";
            }
            else if (isPhysicsPlacing)
            {
                statusText = $"{prefab.name} (Physics Placing...)";
            }
            else
            {
                statusText = $"{prefab.name} (Ready for Physics)";
            }

            Handles.color = Color.white;
            Handles.Label(physicsDropPosition + Vector3.up * 2f, statusText);
            Handles.Label(physicsBrushPosition + Vector3.up * 1f, $"Drop Height: {painter.dropHeight:F1}m");
        }

        // Show placement instructions
        if (!isPhysicsPlacing)
        {
            Handles.color = Color.white;
            if (canPlace)
            {
                Handles.Label(physicsBrushPosition + Vector3.up * 3f, "Shift + Click/Drag for physics placement");
            }
            else
            {
                Handles.Label(physicsBrushPosition + Vector3.up * 3f, "Prefab needs Collider and Rigidbody!");
            }
        }
    }

    void ResizeGrid(Vector3 worldPos)
    {
        Vector3 gridMin = gridCenter - new Vector3(gridSize.x / 2f, 0, gridSize.y / 2f);
        Vector3 gridMax = gridCenter + new Vector3(gridSize.x / 2f, 0, gridSize.y / 2f);

        switch (activeHandle)
        {
            case ResizeHandle.TopLeft:
                gridMin.x = worldPos.x;
                gridMax.z = worldPos.z;
                break;
            case ResizeHandle.TopRight:
                gridMax.x = worldPos.x;
                gridMax.z = worldPos.z;
                break;
            case ResizeHandle.BottomLeft:
                gridMin.x = worldPos.x;
                gridMin.z = worldPos.z;
                break;
            case ResizeHandle.BottomRight:
                gridMax.x = worldPos.x;
                gridMin.z = worldPos.z;
                break;
            case ResizeHandle.Top:
                gridMax.z = worldPos.z;
                break;
            case ResizeHandle.Bottom:
                gridMin.z = worldPos.z;
                break;
            case ResizeHandle.Left:
                gridMin.x = worldPos.x;
                break;
            case ResizeHandle.Right:
                gridMax.x = worldPos.x;
                break;
        }

        // Ensure minimum size
        if (gridMax.x - gridMin.x < 0.5f)
        {
            if (activeHandle == ResizeHandle.Left || activeHandle == ResizeHandle.TopLeft || activeHandle == ResizeHandle.BottomLeft)
                gridMin.x = gridMax.x - 0.5f;
            else
                gridMax.x = gridMin.x + 0.5f;
        }

        if (gridMax.z - gridMin.z < 0.5f)
        {
            if (activeHandle == ResizeHandle.Bottom || activeHandle == ResizeHandle.BottomLeft || activeHandle == ResizeHandle.BottomRight)
                gridMin.z = gridMax.z - 0.5f;
            else
                gridMax.z = gridMin.z + 0.5f;
        }

        // Update grid properties
        gridCenter = (gridMin + gridMax) / 2f;
        gridSize = new Vector2(gridMax.x - gridMin.x, gridMax.z - gridMin.z);

        CalculateGridSize();
    }

    void DrawResizeHandles()
    {
        // Get handle positions
        Vector3 topLeft = gridCenter + new Vector3(-gridSize.x/2, 0, gridSize.y/2);
        Vector3 topRight = gridCenter + new Vector3(gridSize.x/2, 0, gridSize.y/2);
        Vector3 bottomLeft = gridCenter + new Vector3(-gridSize.x/2, 0, -gridSize.y/2);
        Vector3 bottomRight = gridCenter + new Vector3(gridSize.x/2, 0, -gridSize.y/2);

        Vector3 top = gridCenter + new Vector3(0, 0, gridSize.y/2);
        Vector3 bottom = gridCenter + new Vector3(0, 0, -gridSize.y/2);
        Vector3 left = gridCenter + new Vector3(-gridSize.x/2, 0, 0);
        Vector3 right = gridCenter + new Vector3(gridSize.x/2, 0, 0);

        // Draw resize border first
        Handles.color = painter.resizeHandleColor;
        Vector3[] borderPoints = {
            topLeft, topRight, bottomRight, bottomLeft, topLeft
        };
        Handles.DrawPolyLine(borderPoints);

        // Draw handles using Unity's built-in handle styles
        float handleSize = HandleUtility.GetHandleSize(gridCenter) * 0.1f;

        // Corner handles (squares)
        Handles.color = painter.resizeHandleColor;
        DrawSquareHandle(topLeft, handleSize);
        DrawSquareHandle(topRight, handleSize);
        DrawSquareHandle(bottomLeft, handleSize);
        DrawSquareHandle(bottomRight, handleSize);

        // Edge handles (rectangles)
        Handles.color = painter.resizeHandleColor * 0.8f;
        DrawRectHandle(top, handleSize * 0.8f, true);
        DrawRectHandle(bottom, handleSize * 0.8f, true);
        DrawRectHandle(left, handleSize * 0.8f, false);
        DrawRectHandle(right, handleSize * 0.8f, false);
    }

    void DrawSquareHandle(Vector3 position, float size)
    {
        Vector3[] points = {
            position + new Vector3(-size, 0, -size),
            position + new Vector3(size, 0, -size),
            position + new Vector3(size, 0, size),
            position + new Vector3(-size, 0, size),
            position + new Vector3(-size, 0, -size)
        };
        Handles.DrawSolidRectangleWithOutline(points, Handles.color, Color.black);
    }

    void DrawRectHandle(Vector3 position, float size, bool horizontal)
    {
        Vector3[] points;
        if (horizontal)
        {
            points = new Vector3[] {
                position + new Vector3(-size * 1.5f, 0, -size * 0.5f),
                position + new Vector3(size * 1.5f, 0, -size * 0.5f),
                position + new Vector3(size * 1.5f, 0, size * 0.5f),
                position + new Vector3(-size * 1.5f, 0, size * 0.5f),
                position + new Vector3(-size * 1.5f, 0, -size * 0.5f)
            };
        }
        else
        {
            points = new Vector3[] {
                position + new Vector3(-size * 0.5f, 0, -size * 1.5f),
                position + new Vector3(size * 0.5f, 0, -size * 1.5f),
                position + new Vector3(size * 0.5f, 0, size * 1.5f),
                position + new Vector3(-size * 0.5f, 0, size * 1.5f),
                position + new Vector3(-size * 0.5f, 0, -size * 1.5f)
            };
        }
        Handles.DrawSolidRectangleWithOutline(points, Handles.color, Color.black);
    }
    
    // Helper method for cross-version compatibility
    Vector2 GetWorldToScreenPoint(Vector3 worldPos)
    {
        SceneView sceneView = SceneView.currentDrawingSceneView;
        if (sceneView == null || sceneView.camera == null)
            return Vector2.zero;
            
        Camera sceneCamera = sceneView.camera;
        Vector3 screenPoint = sceneCamera.WorldToScreenPoint(worldPos);
        
        // Convert to GUI coordinates (flip Y)
        screenPoint.y = sceneCamera.pixelHeight - screenPoint.y;
        
        return screenPoint;
    }
}
