Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
D:/My Project/Test Project
-logFile
Logs/AssetImportWorker4.log
-srvPort
50213
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Test Project
D:/My Project/Test Project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [18356]  Target information:

Player connection [18356]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2776064790 [EditorId] 2776064790 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [18356]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2776064790 [EditorId] 2776064790 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 201.55 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 7.81 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Test Project/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56588
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.935373 seconds.
- Loaded All Assemblies, in 20.285 seconds
Native extension for Android target not found
Unable to list connected devices. 
* daemon not running; starting now at tcp:5037
could not read ok from ADB Server
* failed to start daemon
adb.exe: failed to check server version: cannot connect to daemon

UnityEditor.Android.CommandInvokationFailure: Unable to list connected devices. 
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\platform-tools\adb.exe devices -l

Environment Variables:
KMP_BLOCKTIME = 0
OMP_WAIT_POLICY = PASSIVE
USERDOMAIN = DESKTOP-GMG2SOO
ProgramFiles = C:\Program Files
TMP = C:\Users\<USER>\AppData\Local\Temp
PROCESSOR_ARCHITECTURE = AMD64
PROCESSOR_REVISION = 3f02
OS = Windows_NT
CHROME_CRASHPAD_PIPE_NAME = \\.\pipe\crashpad_8076_LJXPRUGRNGSUONGS
PROCESSOR_IDENTIFIER = Intel64 Family 6 Model 63 Stepping 2, GenuineIntel
ProgramW6432 = C:\Program Files
USERPROFILE = C:\Users\<USER>\Program Files\Common Files
DriverData = C:\Windows\System32\Drivers\DriverData
ComSpec = C:\Windows\system32\cmd.exe
PSModulePath = C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC = C:\Users\<USER>\Users\ALITAJ~1\AppData\Local\Temp
Path = c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Muse Hub\lib;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32\config\systemprofile\AppData\Local\Muse Hub\lib;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Muse Hub\lib;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin
COMPUTERNAME = DESKTOP-GMG2SOO
PATHEXT = .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
ALLUSERSPROFILE = C:\ProgramData
SystemDrive = C:
windir = C:\Windows
FPS_BROWSER_USER_PROFILE_STRING = Default
HOMEPATH = \Users\Ali Taj
CommonProgramFiles(x86) = C:\Program Files (x86)\Common Files
APPDATA = C:\Users\<USER>\AppData\Roaming
OneDrive = C:\Users\<USER>\OneDrive
PROCESSOR_LEVEL = 6
CommonProgramW6432 = C:\Program Files\Common Files
ProgramFiles(x86) = C:\Program Files (x86)
SystemRoot = C:\Windows
SESSIONNAME = Console
LOGONSERVER = \\DESKTOP-GMG2SOO
LOCALAPPDATA = C:\Users\<USER>\AppData\Local
VBOX_MSI_INSTALL_PATH = C:\Program Files\Oracle\VirtualBox\
HOMEDRIVE = C:
USERDOMAIN_ROAMINGPROFILE = DESKTOP-GMG2SOO
ProgramData = C:\ProgramData
ORIGINAL_XDG_CURRENT_DESKTOP = undefined


stderr[
* daemon not running; starting now at tcp:5037
could not read ok from ADB Server
* failed to start daemon
adb.exe: failed to check server version: cannot connect to daemon
]
stdout[

]
exit code: 1
  at UnityEditor.Android.Command.WaitForProgramToRun (UnityEditor.Utils.Program p, UnityEditor.Android.Command+WaitingForProcessToExit waitingForProcessToExit, System.String errorMsg) [0x00033] in <baa85b046c844c2db706850a8462d4ac>:0 
  at UnityEditor.Android.Command.Run (System.Diagnostics.ProcessStartInfo psi, UnityEditor.Android.Command+WaitingForProcessToExit waitingForProcessToExit, System.String errorMsg) [0x00017] in <baa85b046c844c2db706850a8462d4ac>:0 
  at UnityEditor.Android.Command.Run (System.String command, System.String args, System.String workingdir, UnityEditor.Android.Command+WaitingForProcessToExit waitingForProcessToExit, System.String errorMsg, System.Text.Encoding inputEncoding) [0x0000c] in <baa85b046c844c2db706850a8462d4ac>:0 
  at UnityEditor.Android.ADB.RunInternal (System.String[] command, UnityEditor.Android.Command+WaitingForProcessToExit waitingForProcessToExit, System.String errorMsg) [0x0001e] in <baa85b046c844c2db706850a8462d4ac>:0 
  at UnityEditor.Android.ADB.Run (System.String[] command, UnityEditor.Android.Command+WaitingForProcessToExit waitingForProcessToExit, System.String errorMsg) [0x00013] in <baa85b046c844c2db706850a8462d4ac>:0 
  at UnityEditor.Android.AndroidDeploymentTargetsExtension.GetKnownTargets (UnityEditor.DeploymentTargets.IDeploymentTargetsMainThreadContext context, UnityEditor.ProgressHandler progressHandler) [0x00117] in <baa85b046c844c2db706850a8462d4ac>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEditor.Android.AndroidDeploymentTargetsExtension:GetKnownTargets (UnityEditor.DeploymentTargets.IDeploymentTargetsMainThreadContext,UnityEditor.ProgressHandler)
UnityEditor.Android.TargetScanWorker:ScanSync ()
UnityEditor.Android.TargetExtension:OnUsbDevicesChanged (UnityEditor.Hardware.UsbDevice[])
UnityEditor.Android.TargetExtension:OnLoad ()
UnityEditor.Modules.ModuleManager:InitializePlatformSupportModules ()

Android Extension - Scanning For ADB Devices 5135 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.860 seconds
Domain Reload Profiling: 26142ms
	BeginReloadAssembly (16020ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (628ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (1019ms)
	LoadAllAssembliesAndSetupDomain (2590ms)
		LoadAssemblies (16015ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2584ms)
			TypeCache.Refresh (2581ms)
				TypeCache.ScanAssembly (2550ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (5861ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5753ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5340ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (203ms)
			ProcessInitializeOnLoadMethodAttributes (110ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 19.471 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 2.64 ms, found 5 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.457 seconds
Domain Reload Profiling: 20923ms
	BeginReloadAssembly (325ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (69ms)
	LoadAllAssembliesAndSetupDomain (18982ms)
		LoadAssemblies (17363ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1816ms)
			TypeCache.Refresh (1649ms)
				TypeCache.ScanAssembly (1515ms)
			BuildScriptInfoCaches (146ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1458ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1258ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (281ms)
			ProcessInitializeOnLoadAttributes (710ms)
			ProcessInitializeOnLoadMethodAttributes (229ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
database is locked
Failed to delete database file Library/ShaderCache.db
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 4.21 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.11 ms.
Unloading 76 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6843 unused Assets / (5.3 MB). Loaded Objects now: 7347.
Memory consumption went from 218.2 MB to 212.9 MB.
Total: 22.733400 ms (FindLiveObjects: 1.550200 ms CreateObjectMapping: 1.018000 ms MarkObjects: 13.844900 ms  DeleteObjects: 6.316200 ms)

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0