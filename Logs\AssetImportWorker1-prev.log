Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/My Project/Test Project
-logFile
Logs/AssetImportWorker1.log
-srvPort
50213
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Test Project
D:/My Project/Test Project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [16984]  Target information:

Player connection [16984]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3435685957 [EditorId] 3435685957 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [16984]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3435685957 [EditorId] 3435685957 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 264.89 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 8.49 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Test Project/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56192
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.912249 seconds.
- Loaded All Assemblies, in 20.280 seconds
Native extension for Android target not found
InvalidOperationException: Cannot process request because the process (1496) has exited.
  at System.Diagnostics.Process.GetProcessHandle (System.Int32 access, System.Boolean throwIfExited) [0x0004e] in <e243450672844c6f9f403082ca9f1be7>:0 
  at System.Diagnostics.Process.GetProcessHandle (System.Int32 access) [0x00000] in <e243450672844c6f9f403082ca9f1be7>:0 
  at System.Diagnostics.Process.Kill () [0x00002] in <e243450672844c6f9f403082ca9f1be7>:0 
  at (wrapper remoting-invoke-with-check) System.Diagnostics.Process.Kill()
  at UnityEditor.Android.AndroidDeploymentTargetsExtension.GetKnownTargets (UnityEditor.DeploymentTargets.IDeploymentTargetsMainThreadContext context, UnityEditor.ProgressHandler progressHandler) [0x00174] in <baa85b046c844c2db706850a8462d4ac>:0 
  at UnityEditor.Android.TargetScanWorker.ScanSync () [0x00049] in <baa85b046c844c2db706850a8462d4ac>:0 
  at UnityEditor.Android.TargetExtension.OnUsbDevicesChanged (UnityEditor.Hardware.UsbDevice[] usbDevices) [0x00087] in <baa85b046c844c2db706850a8462d4ac>:0 
  at UnityEditor.Android.TargetExtension.OnLoad () [0x000af] in <baa85b046c844c2db706850a8462d4ac>:0 
  at UnityEditor.Modules.ModuleManager.InitializePlatformSupportModules () [0x000ba] in <4e9c48eedf6f482fa439ccf335631481>:0 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
UnityEditor.Modules.ModuleManager:InitializePlatformSupportModules()

Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.231 seconds
Domain Reload Profiling: 23506ms
	BeginReloadAssembly (16017ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (648ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (1016ms)
	LoadAllAssembliesAndSetupDomain (2567ms)
		LoadAssemblies (16013ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2561ms)
			TypeCache.Refresh (2558ms)
				TypeCache.ScanAssembly (2527ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (3231ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3129ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2691ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (223ms)
			ProcessInitializeOnLoadMethodAttributes (108ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 19.415 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 2.78 ms, found 5 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.486 seconds
Domain Reload Profiling: 20896ms
	BeginReloadAssembly (323ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (73ms)
	LoadAllAssembliesAndSetupDomain (18926ms)
		LoadAssemblies (17322ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1798ms)
			TypeCache.Refresh (1648ms)
				TypeCache.ScanAssembly (1502ms)
			BuildScriptInfoCaches (132ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1487ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1282ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (279ms)
			ProcessInitializeOnLoadAttributes (732ms)
			ProcessInitializeOnLoadMethodAttributes (232ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Launched and connected shader compiler UnityShaderCompiler.exe after 0.06 seconds
Refreshing native plugins compatible for Editor in 3.36 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.06 ms.
Unloading 76 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6843 unused Assets / (5.2 MB). Loaded Objects now: 7347.
Memory consumption went from 216.4 MB to 211.2 MB.
Total: 23.376600 ms (FindLiveObjects: 2.115600 ms CreateObjectMapping: 1.240300 ms MarkObjects: 12.820700 ms  DeleteObjects: 7.194900 ms)

========================================================================
Received Import Request.
  Time since last request: 3684.841030 seconds.
  path: Assets/Scarecrow Animated Low-poly/textures/Base.png
  artifactKey: Guid(a7dc72ca55594a14c873da80421d9e2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scarecrow Animated Low-poly/textures/Base.png using Guid(a7dc72ca55594a14c873da80421d9e2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dbfca1084929c6f79c3bf0df16a0019f') in 0.4139097 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/screw/babloo.fbm/scarecrow_texture.png
  artifactKey: Guid(a55ab98bb79cdab478e541d082b61b15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/screw/babloo.fbm/scarecrow_texture.png using Guid(a55ab98bb79cdab478e541d082b61b15) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '875ecd72680b632775b1a0e22b38a07a') in 0.1140403 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/plugh.png
  artifactKey: Guid(e3cf91a8e213f4742a186cda47a6b3bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/plugh.png using Guid(e3cf91a8e213f4742a186cda47a6b3bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0faea57b702f42709414f2917e20d7c6') in 0.0528335 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Scarecrow Animated Low-poly/textures/ScareCrow_Bdy_Metalness.png
  artifactKey: Guid(d5f3e3619a880f94987b5264a2536473) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scarecrow Animated Low-poly/textures/ScareCrow_Bdy_Metalness.png using Guid(d5f3e3619a880f94987b5264a2536473) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2259a30b63738faed1f34d6f97c8e901') in 0.1545133 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/TRACTOR1.fbm/Textures 1.png
  artifactKey: Guid(e40c0c32434bdf44eb2e3ab14a65d670) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TRACTOR1.fbm/Textures 1.png using Guid(e40c0c32434bdf44eb2e3ab14a65d670) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b042b02017c66cf590a0c5d6d598852f') in 0.1253353 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0