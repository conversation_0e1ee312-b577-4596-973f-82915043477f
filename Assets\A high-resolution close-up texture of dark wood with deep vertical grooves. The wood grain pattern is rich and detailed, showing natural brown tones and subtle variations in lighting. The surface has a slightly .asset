%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 31a6b9796d1c0440eba061ace5e9dff6, type: 3}
  m_Name: 'A high-resolution close-up texture of dark wood with deep vertical grooves.
    The wood grain pattern is rich and detailed, showing natural brown tones and
    subtle variations in lighting. The surface has a slightly '
  m_EditorClassIdentifier: 
  assetsData:
  - rid: 4911230938521272631
  - rid: 4911230938521272637
  - rid: 4911230938521272643
  - rid: 4911230938521272649
  - rid: 4911230938521272655
  - rid: 4911230938521272661
  - rid: 4911230938521272667
  - rid: 4911230938521272673
  - rid: 4911230938521272679
  - rid: 4911230938521272685
  - rid: 4911230938521272691
  - rid: 4911230938521272697
  currentMode: TextToImage
  m_Data:
  - rid: 4911230938521272623
  - rid: 4911230938521272624
  - rid: 4911230938521272625
  preRefinedArtifact:
    rid: -2
  refinedArtifact:
    rid: -2
  selectedArtifact:
    rid: 4911230938521272637
  m_Operators:
  - rid: 4911230938521272626
  - rid: 4911230938521272627
  - rid: 4911230938521272628
  - rid: 4911230938521272629
  - rid: 4911230938521272630
  m_PreRefineOperators: []
  m_ExportedArtifacts:
  - m_UnityGuid: bbb1d34a548367949b29f9a87d0309d0
    m_MuseGuid: a7d23a33-f647-4687-aafd-6c194702da64
  references:
    version: 2
    RefIds:
    - rid: -2
      type: {class: , ns: , asm: }
    - rid: 4911230938521272623
      type: {class: BookmarkManager, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_BookmarkedArtifacts:
          m_Values: []
        m_IsFilterEnabled: 0
    - rid: 4911230938521272624
      type: {class: MainUI/UISize, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_NodeListWidth: 300
        m_NodeListRefineWidth: 300
        m_AssetListRefineWidth: 200
    - rid: 4911230938521272625
      type: {class: GenerateButtonData, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data: 
    - rid: 4911230938521272626
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272627
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A vast, open field of prepared soil, with numerous parallel rows of furrows.
            The earth is a rich, warm brown color, showing textures of tilled soil
            and shadowed areas.  The rows stretch out into the distance, creating
            a straight, parallel pattern that extends across the entire image.  A
            lighter-colored field is visible in the distance to the right side of
            the image, suggesting another crop or area. The sky is a light blue,
            with scattered clouds.  The light appears to be coming from slightly
            above and in front of the viewer, casting subtle shadows within the rows.
            The overall perspective is directly above the furrows, emphasizing the
            organized agricultural layout. The image conveys a sense of a sunny day
            and a large, cultivated expanse.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272628
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272629
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272630
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272631
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: efed3d1a-a158-4613-b250-d58c555e902a
        BatchRequestIdentifier: 
        Seed: 2718110210
        m_Operators:
        - rid: 4911230938521272632
        - rid: 4911230938521272633
        - rid: 4911230938521272634
        - rid: 4911230938521272635
        - rid: 4911230938521272636
        history:
        - rid: 4911230938521272631
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272632
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272633
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A high-resolution close-up texture of dark wood with deep vertical grooves.
            The wood grain pattern is rich and detailed, showing natural brown tones
            and subtle variations in lighting. The surface has a slightly glossy
            finish, enhancing the depth and contrast of the linear ridges.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272634
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272635
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272636
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272637
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: a7d23a33-f647-4687-aafd-6c194702da64
        BatchRequestIdentifier: 
        Seed: 139177261
        m_Operators:
        - rid: 4911230938521272638
        - rid: 4911230938521272639
        - rid: 4911230938521272640
        - rid: 4911230938521272641
        - rid: 4911230938521272642
        history:
        - rid: 4911230938521272637
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272638
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272639
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A high-resolution close-up texture of dark wood with deep vertical grooves.
            The wood grain pattern is rich and detailed, showing natural brown tones
            and subtle variations in lighting. The surface has a slightly glossy
            finish, enhancing the depth and contrast of the linear ridges.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272640
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272641
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272642
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272643
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: 06a79d9f-83f0-4e1f-9da6-6a5f28ae37e4
        BatchRequestIdentifier: 
        Seed: 2041678272
        m_Operators:
        - rid: 4911230938521272644
        - rid: 4911230938521272645
        - rid: 4911230938521272646
        - rid: 4911230938521272647
        - rid: 4911230938521272648
        history:
        - rid: 4911230938521272643
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272644
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272645
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A high-resolution close-up texture of dark wood with deep vertical grooves.
            The wood grain pattern is rich and detailed, showing natural brown tones
            and subtle variations in lighting. The surface has a slightly glossy
            finish, enhancing the depth and contrast of the linear ridges.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272646
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272647
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272648
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272649
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: f2bbeee9-6b69-466d-9217-dd6763eb9409
        BatchRequestIdentifier: 
        Seed: 3778222677
        m_Operators:
        - rid: 4911230938521272650
        - rid: 4911230938521272651
        - rid: 4911230938521272652
        - rid: 4911230938521272653
        - rid: 4911230938521272654
        history:
        - rid: 4911230938521272649
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272650
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272651
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A high-resolution close-up texture of dark wood with deep vertical grooves.
            The wood grain pattern is rich and detailed, showing natural brown tones
            and subtle variations in lighting. The surface has a slightly glossy
            finish, enhancing the depth and contrast of the linear ridges.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272652
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272653
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272654
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272655
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: 196342d3-10ce-4963-8dc2-1424bcbba73f
        BatchRequestIdentifier: 
        Seed: 509211475
        m_Operators:
        - rid: 4911230938521272656
        - rid: 4911230938521272657
        - rid: 4911230938521272658
        - rid: 4911230938521272659
        - rid: 4911230938521272660
        history:
        - rid: 4911230938521272655
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272656
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272657
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A wide-angle view of a freshly plowed farm field at golden hour, with
            long, perfectly parallel furrows stretching toward the horizon. The ridges
            and grooves in the soil are sharply defined, creating a strong sense
            of depth and symmetry.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272658
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272659
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272660
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272661
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: aa9be023-3e02-4b3e-a749-526b54c44b19
        BatchRequestIdentifier: 
        Seed: 3061802572
        m_Operators:
        - rid: 4911230938521272662
        - rid: 4911230938521272663
        - rid: 4911230938521272664
        - rid: 4911230938521272665
        - rid: 4911230938521272666
        history:
        - rid: 4911230938521272661
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272662
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272663
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A wide-angle view of a freshly plowed farm field at golden hour, with
            long, perfectly parallel furrows stretching toward the horizon. The ridges
            and grooves in the soil are sharply defined, creating a strong sense
            of depth and symmetry.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272664
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272665
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272666
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272667
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: 002169f2-c604-4f40-828f-09765ecc44e1
        BatchRequestIdentifier: 
        Seed: 258821456
        m_Operators:
        - rid: 4911230938521272668
        - rid: 4911230938521272669
        - rid: 4911230938521272670
        - rid: 4911230938521272671
        - rid: 4911230938521272672
        history:
        - rid: 4911230938521272667
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272668
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272669
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A wide-angle view of a freshly plowed farm field at golden hour, with
            long, perfectly parallel furrows stretching toward the horizon. The ridges
            and grooves in the soil are sharply defined, creating a strong sense
            of depth and symmetry.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272670
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272671
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272672
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272673
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: 8b2001b2-f9a7-4452-afe4-0152792bd314
        BatchRequestIdentifier: 
        Seed: 1484530357
        m_Operators:
        - rid: 4911230938521272674
        - rid: 4911230938521272675
        - rid: 4911230938521272676
        - rid: 4911230938521272677
        - rid: 4911230938521272678
        history:
        - rid: 4911230938521272673
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272674
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272675
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A wide-angle view of a freshly plowed farm field at golden hour, with
            long, perfectly parallel furrows stretching toward the horizon. The ridges
            and grooves in the soil are sharply defined, creating a strong sense
            of depth and symmetry.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272676
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272677
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272678
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272679
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: 793861c4-3339-47e6-9264-e002844aaf75
        BatchRequestIdentifier: 
        Seed: 3788185103
        m_Operators:
        - rid: 4911230938521272680
        - rid: 4911230938521272681
        - rid: 4911230938521272682
        - rid: 4911230938521272683
        - rid: 4911230938521272684
        history:
        - rid: 4911230938521272679
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272680
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272681
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A vast, open field of prepared soil, with numerous parallel rows of furrows.
            The earth is a rich, warm brown color, showing textures of tilled soil
            and shadowed areas.  The rows stretch out into the distance, creating
            a straight, parallel pattern that extends across the entire image.  A
            lighter-colored field is visible in the distance to the right side of
            the image, suggesting another crop or area. The sky is a light blue,
            with scattered clouds.  The light appears to be coming from slightly
            above and in front of the viewer, casting subtle shadows within the rows.
            The overall perspective is directly above the furrows, emphasizing the
            organized agricultural layout. The image conveys a sense of a sunny day
            and a large, cultivated expanse.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272682
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272683
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272684
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272685
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: 748614c6-5019-4a8f-9b55-da10d08d26d1
        BatchRequestIdentifier: 
        Seed: 3472209375
        m_Operators:
        - rid: 4911230938521272686
        - rid: 4911230938521272687
        - rid: 4911230938521272688
        - rid: 4911230938521272689
        - rid: 4911230938521272690
        history:
        - rid: 4911230938521272685
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272686
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272687
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A vast, open field of prepared soil, with numerous parallel rows of furrows.
            The earth is a rich, warm brown color, showing textures of tilled soil
            and shadowed areas.  The rows stretch out into the distance, creating
            a straight, parallel pattern that extends across the entire image.  A
            lighter-colored field is visible in the distance to the right side of
            the image, suggesting another crop or area. The sky is a light blue,
            with scattered clouds.  The light appears to be coming from slightly
            above and in front of the viewer, casting subtle shadows within the rows.
            The overall perspective is directly above the furrows, emphasizing the
            organized agricultural layout. The image conveys a sense of a sunny day
            and a large, cultivated expanse.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272688
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272689
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272690
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272691
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: a3087f84-06b4-49f4-8d8d-bad98ee76ff8
        BatchRequestIdentifier: 
        Seed: 3582592633
        m_Operators:
        - rid: 4911230938521272692
        - rid: 4911230938521272693
        - rid: 4911230938521272694
        - rid: 4911230938521272695
        - rid: 4911230938521272696
        history:
        - rid: 4911230938521272691
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272692
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272693
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A vast, open field of prepared soil, with numerous parallel rows of furrows.
            The earth is a rich, warm brown color, showing textures of tilled soil
            and shadowed areas.  The rows stretch out into the distance, creating
            a straight, parallel pattern that extends across the entire image.  A
            lighter-colored field is visible in the distance to the right side of
            the image, suggesting another crop or area. The sky is a light blue,
            with scattered clouds.  The light appears to be coming from slightly
            above and in front of the viewer, casting subtle shadows within the rows.
            The overall perspective is directly above the furrows, emphasizing the
            organized agricultural layout. The image conveys a sense of a sunny day
            and a large, cultivated expanse.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272694
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272695
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272696
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272697
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: ccf5fd61-c972-473f-a5d4-beccd137667b
        BatchRequestIdentifier: 
        Seed: 3160196188
        m_Operators:
        - rid: 4911230938521272698
        - rid: 4911230938521272699
        - rid: 4911230938521272700
        - rid: 4911230938521272701
        - rid: 4911230938521272702
        history:
        - rid: 4911230938521272697
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272698
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272699
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A vast, open field of prepared soil, with numerous parallel rows of furrows.
            The earth is a rich, warm brown color, showing textures of tilled soil
            and shadowed areas.  The rows stretch out into the distance, creating
            a straight, parallel pattern that extends across the entire image.  A
            lighter-colored field is visible in the distance to the right side of
            the image, suggesting another crop or area. The sky is a light blue,
            with scattered clouds.  The light appears to be coming from slightly
            above and in front of the viewer, casting subtle shadows within the rows.
            The overall perspective is directly above the furrows, emphasizing the
            organized agricultural layout. The image conveys a sense of a sunny day
            and a large, cultivated expanse.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272700
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272701
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272702
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
