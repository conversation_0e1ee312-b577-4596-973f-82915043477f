Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/My Project/Test Project
-logFile
Logs/AssetImportWorker0.log
-srvPort
50213
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Test Project
D:/My Project/Test Project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [13068]  Target information:

Player connection [13068]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 281155940 [EditorId] 281155940 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13068]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 281155940 [EditorId] 281155940 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 263.89 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 6.61 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Test Project/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56380
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.912998 seconds.
- Loaded All Assemblies, in 20.272 seconds
Native extension for Android target not found
Multiple ADB server instances found, the following ADB server instance have been terminated due to being run from another SDK. Process paths: 
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\platform-tools\adb.exe
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityEditor.Android.AndroidDeploymentTargetsExtension:GetKnownTargets (UnityEditor.DeploymentTargets.IDeploymentTargetsMainThreadContext,UnityEditor.ProgressHandler)
UnityEditor.Android.TargetScanWorker:ScanSync ()
UnityEditor.Android.TargetExtension:OnUsbDevicesChanged (UnityEditor.Hardware.UsbDevice[])
UnityEditor.Android.TargetExtension:OnLoad ()
UnityEditor.Modules.ModuleManager:InitializePlatformSupportModules ()

Android Extension - Scanning For ADB Devices 2528 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.265 seconds
Domain Reload Profiling: 23532ms
	BeginReloadAssembly (16018ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (647ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (1013ms)
	LoadAllAssembliesAndSetupDomain (2562ms)
		LoadAssemblies (16013ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2556ms)
			TypeCache.Refresh (2553ms)
				TypeCache.ScanAssembly (2524ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (3266ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3152ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2733ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (201ms)
			ProcessInitializeOnLoadMethodAttributes (106ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 19.416 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 2.67 ms, found 5 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.462 seconds
Domain Reload Profiling: 20873ms
	BeginReloadAssembly (322ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (73ms)
	LoadAllAssembliesAndSetupDomain (18926ms)
		LoadAssemblies (17328ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1792ms)
			TypeCache.Refresh (1642ms)
				TypeCache.ScanAssembly (1497ms)
			BuildScriptInfoCaches (132ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1463ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1264ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (266ms)
			ProcessInitializeOnLoadAttributes (724ms)
			ProcessInitializeOnLoadMethodAttributes (235ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 3.78 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.06 ms.
Unloading 76 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6843 unused Assets / (5.9 MB). Loaded Objects now: 7347.
Memory consumption went from 220.2 MB to 214.2 MB.
Total: 23.096600 ms (FindLiveObjects: 1.781500 ms CreateObjectMapping: 1.038600 ms MarkObjects: 11.073400 ms  DeleteObjects: 9.198300 ms)

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0