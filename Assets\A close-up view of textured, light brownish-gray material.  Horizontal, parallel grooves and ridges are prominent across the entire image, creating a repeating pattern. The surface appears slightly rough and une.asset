%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 31a6b9796d1c0440eba061ace5e9dff6, type: 3}
  m_Name: A close-up view of textured, light brownish-gray material.  Horizontal,
    parallel grooves and ridges are prominent across the entire image, creating a
    repeating pattern. The surface appears slightly rough and une
  m_EditorClassIdentifier: 
  assetsData:
  - rid: 4911230938521272743
  - rid: 4911230938521272749
  - rid: 4911230938521272755
  - rid: 4911230938521272761
  currentMode: TextToImage
  m_Data:
  - rid: 4911230938521272735
  - rid: 4911230938521272736
  - rid: 4911230938521272737
  preRefinedArtifact:
    rid: -2
  refinedArtifact:
    rid: -2
  selectedArtifact:
    rid: 4911230938521272743
  m_Operators:
  - rid: 4911230938521272738
  - rid: 4911230938521272739
  - rid: 4911230938521272740
  - rid: 4911230938521272741
  - rid: 4911230938521272742
  m_PreRefineOperators: []
  m_ExportedArtifacts:
  - m_UnityGuid: 5d8ff31fb17c0e44a9236df2f6807bb3
    m_MuseGuid: bc703883-fe40-4b4a-84db-4c93b9873f57
  references:
    version: 2
    RefIds:
    - rid: -2
      type: {class: , ns: , asm: }
    - rid: 4911230938521272735
      type: {class: BookmarkManager, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_BookmarkedArtifacts:
          m_Values: []
        m_IsFilterEnabled: 0
    - rid: 4911230938521272736
      type: {class: MainUI/UISize, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_NodeListWidth: 300
        m_NodeListRefineWidth: 300
        m_AssetListRefineWidth: 200
    - rid: 4911230938521272737
      type: {class: GenerateButtonData, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data: 
    - rid: 4911230938521272738
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272739
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A close-up view of textured, light brownish-gray material.  Horizontal,
            parallel grooves and ridges are prominent across the entire image, creating
            a repeating pattern. The surface appears slightly rough and uneven, with
            variations in tone and depth within the grooves. The light is even and
            diffused, casting no significant shadows.  The overall impression is
            of a natural material, possibly compacted earth, sand, or a similar substance,
            with an emphasis on its textural qualities rather than any specific details
            or objects. The composition is entirely filled with the repeating pattern,
            no other elements are visible. The perspective is directly facing the
            surface, with no depth or 3D aspects visible.  The color palette is limited
            to various shades of light brown, gray, and beige. The atmosphere is
            neutral and understated. The style is abstract and emphasizes the pattern
            and texture of the material.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272740
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272741
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272742
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272743
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: bc703883-fe40-4b4a-84db-4c93b9873f57
        BatchRequestIdentifier: 
        Seed: 1722913621
        m_Operators:
        - rid: 4911230938521272744
        - rid: 4911230938521272745
        - rid: 4911230938521272746
        - rid: 4911230938521272747
        - rid: 4911230938521272748
        history:
        - rid: 4911230938521272743
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272744
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272745
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A close-up view of textured, light brownish-gray material.  Horizontal,
            parallel grooves and ridges are prominent across the entire image, creating
            a repeating pattern. The surface appears slightly rough and uneven, with
            variations in tone and depth within the grooves. The light is even and
            diffused, casting no significant shadows.  The overall impression is
            of a natural material, possibly compacted earth, sand, or a similar substance,
            with an emphasis on its textural qualities rather than any specific details
            or objects. The composition is entirely filled with the repeating pattern,
            no other elements are visible. The perspective is directly facing the
            surface, with no depth or 3D aspects visible.  The color palette is limited
            to various shades of light brown, gray, and beige. The atmosphere is
            neutral and understated. The style is abstract and emphasizes the pattern
            and texture of the material.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272746
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272747
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272748
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272749
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: 8c6b15d0-dcdc-44e6-a48c-d771f2ec0162
        BatchRequestIdentifier: 
        Seed: 740182886
        m_Operators:
        - rid: 4911230938521272750
        - rid: 4911230938521272751
        - rid: 4911230938521272752
        - rid: 4911230938521272753
        - rid: 4911230938521272754
        history:
        - rid: 4911230938521272749
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272750
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272751
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A close-up view of textured, light brownish-gray material.  Horizontal,
            parallel grooves and ridges are prominent across the entire image, creating
            a repeating pattern. The surface appears slightly rough and uneven, with
            variations in tone and depth within the grooves. The light is even and
            diffused, casting no significant shadows.  The overall impression is
            of a natural material, possibly compacted earth, sand, or a similar substance,
            with an emphasis on its textural qualities rather than any specific details
            or objects. The composition is entirely filled with the repeating pattern,
            no other elements are visible. The perspective is directly facing the
            surface, with no depth or 3D aspects visible.  The color palette is limited
            to various shades of light brown, gray, and beige. The atmosphere is
            neutral and understated. The style is abstract and emphasizes the pattern
            and texture of the material.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272752
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272753
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272754
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272755
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: 82db31a9-e5b4-4034-b099-1c1ef18c7c04
        BatchRequestIdentifier: 
        Seed: 1940500704
        m_Operators:
        - rid: 4911230938521272756
        - rid: 4911230938521272757
        - rid: 4911230938521272758
        - rid: 4911230938521272759
        - rid: 4911230938521272760
        history:
        - rid: 4911230938521272755
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272756
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272757
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A close-up view of textured, light brownish-gray material.  Horizontal,
            parallel grooves and ridges are prominent across the entire image, creating
            a repeating pattern. The surface appears slightly rough and uneven, with
            variations in tone and depth within the grooves. The light is even and
            diffused, casting no significant shadows.  The overall impression is
            of a natural material, possibly compacted earth, sand, or a similar substance,
            with an emphasis on its textural qualities rather than any specific details
            or objects. The composition is entirely filled with the repeating pattern,
            no other elements are visible. The perspective is directly facing the
            surface, with no depth or 3D aspects visible.  The color palette is limited
            to various shades of light brown, gray, and beige. The atmosphere is
            neutral and understated. The style is abstract and emphasizes the pattern
            and texture of the material.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272758
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272759
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272760
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272761
      type: {class: ImageArtifact, ns: Unity.Muse.Texture, asm: Unity.Muse.Texture}
      data:
        mode: TextToImage
        Guid: 5dcb2770-14cb-4b3c-8c94-e5af5c9848ba
        BatchRequestIdentifier: 
        Seed: 3463219777
        m_Operators:
        - rid: 4911230938521272762
        - rid: 4911230938521272763
        - rid: 4911230938521272764
        - rid: 4911230938521272765
        - rid: 4911230938521272766
        history:
        - rid: 4911230938521272761
        children: []
        IsPbrMode: 0
    - rid: 4911230938521272762
      type: {class: GenerateOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: GenerateOperator
          key: GenerateOperator
          version: 1.0.0
          settings:
          - TextToImage
          - 4
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272763
      type: {class: PromptOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: PromptOperator
          key: PromptOperator
          version: 0.0.2
          settings:
          - A close-up view of textured, light brownish-gray material.  Horizontal,
            parallel grooves and ridges are prominent across the entire image, creating
            a repeating pattern. The surface appears slightly rough and uneven, with
            variations in tone and depth within the grooves. The light is even and
            diffused, casting no significant shadows.  The overall impression is
            of a natural material, possibly compacted earth, sand, or a similar substance,
            with an emphasis on its textural qualities rather than any specific details
            or objects. The composition is entirely filled with the repeating pattern,
            no other elements are visible. The perspective is directly facing the
            surface, with no depth or 3D aspects visible.  The color palette is limited
            to various shades of light brown, gray, and beige. The atmosphere is
            neutral and understated. The style is abstract and emphasizes the pattern
            and texture of the material.
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272764
      type: {class: LoraOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: LoraOperator
          key: LoraOperator
          version: 0.0.1
          settings:
          - guid1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272765
      type: {class: ReferenceOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: ReferenceOperator
          key: ReferenceOperator
          version: 0.0.2
          settings:
          - 
          - 
          - 0
          - 20
          - 
          enabled: 1
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
    - rid: 4911230938521272766
      type: {class: MaskOperator, ns: Unity.Muse.Common, asm: Unity.Muse.Common}
      data:
        m_OperatorData:
          type: MaskOperator
          key: MaskOperator
          version: 0.0.1
          settings:
          - 
          - True
          - 1
          enabled: 0
          assembly: 
          hideable: 0
          hidden: 0
          isRefinement: 0
