Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker8
-projectPath
D:/My Project/Test Project
-logFile
Logs/AssetImportWorker8.log
-srvPort
50213
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Test Project
D:/My Project/Test Project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [10572]  Target information:

Player connection [10572]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3880891607 [EditorId] 3880891607 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [10572]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3880891607 [EditorId] 3880891607 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 4.37 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 4.36 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Test Project/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56328
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.007402 seconds.
- Loaded All Assemblies, in  0.722 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 548 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.170 seconds
Domain Reload Profiling: 1886ms
	BeginReloadAssembly (251ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (90ms)
	LoadAllAssembliesAndSetupDomain (285ms)
		LoadAssemblies (246ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (279ms)
			TypeCache.Refresh (277ms)
				TypeCache.ScanAssembly (250ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1170ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1093ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (703ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (193ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.680 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 2.76 ms, found 5 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.604 seconds
Domain Reload Profiling: 3280ms
	BeginReloadAssembly (299ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (68ms)
	LoadAllAssembliesAndSetupDomain (1220ms)
		LoadAssemblies (711ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (689ms)
			TypeCache.Refresh (499ms)
				TypeCache.ScanAssembly (450ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1605ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1384ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (335ms)
			ProcessInitializeOnLoadAttributes (739ms)
			ProcessInitializeOnLoadMethodAttributes (256ms)
			AfterProcessingInitializeOnLoad (13ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 5.25 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.14 ms.
Unloading 76 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6843 unused Assets / (5.7 MB). Loaded Objects now: 7347.
Memory consumption went from 218.3 MB to 212.7 MB.
Total: 34.364100 ms (FindLiveObjects: 3.224400 ms CreateObjectMapping: 2.131400 ms MarkObjects: 19.141800 ms  DeleteObjects: 9.860900 ms)

========================================================================
Received Import Request.
  Time since last request: 5476.600255 seconds.
  path: Assets/Generated Image July 25, 2025 - 9_35AM (15).jpeg
  artifactKey: Guid(8e5b755d24dddeb4284df08f76d5cefb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Generated Image July 25, 2025 - 9_35AM (15).jpeg using Guid(8e5b755d24dddeb4284df08f76d5cefb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '89bc7ecb421448625f5d527f75a4c98a') in 0.218406 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 13.144292 seconds.
  path: Assets/NewBrush.brush
  artifactKey: Guid(7cc6fdfa5cb448b489930d72a0f19ae1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/NewBrush.brush using Guid(7cc6fdfa5cb448b489930d72a0f19ae1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eb307b54cd52ddb22208017f3ae8fd01') in 0.0923537 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 104.329735 seconds.
  path: Assets/wmremove-transformed.jpeg
  artifactKey: Guid(51f50f5e7903ce74b936a6ad0353b903) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/wmremove-transformed.jpeg using Guid(51f50f5e7903ce74b936a6ad0353b903) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd00c975a9e8f19666c6b4e1c0fc7a36a') in 0.0441265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 175.146202 seconds.
  path: Assets/imgi_90_Farmland0027_3_download600.jpeg
  artifactKey: Guid(ab6bd57cb43709144a1b195cc6ec84b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/imgi_90_Farmland0027_3_download600.jpeg using Guid(ab6bd57cb43709144a1b195cc6ec84b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a465e00a0edf57101cdd5c8749ac434c') in 0.0263822 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 138.005861 seconds.
  path: Assets/wmremove-transformed (2).jpeg
  artifactKey: Guid(7bc41fccce967c5479e339cf2781eed2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/wmremove-transformed (2).jpeg using Guid(7bc41fccce967c5479e339cf2781eed2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '426d44e6e39a887f6e1053638b7373fe') in 0.0836618 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 135.994860 seconds.
  path: Assets/Generated Image July 25, 2025 - 9_45AM.jpeg
  artifactKey: Guid(eaf7f4d3f1b32d64b90a012e92a729c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Generated Image July 25, 2025 - 9_45AM.jpeg using Guid(eaf7f4d3f1b32d64b90a012e92a729c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9871d4a601e1d47ea4ae16f6ffb37945') in 0.0357381 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 72.906161 seconds.
  path: Assets/8cf0a22e-40d3-4e0f-a820-eb6257941dd5.png
  artifactKey: Guid(0cdf71a1979b8444c853dd126db0239e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/8cf0a22e-40d3-4e0f-a820-eb6257941dd5.png using Guid(0cdf71a1979b8444c853dd126db0239e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ff9c81f32eedb2b27b55e0be3c3d52ce') in 0.0298687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 112.872453 seconds.
  path: Assets/bc703883-fe40-4b4a-84db-4c93b9873f57.png
  artifactKey: Guid(5d8ff31fb17c0e44a9236df2f6807bb3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/bc703883-fe40-4b4a-84db-4c93b9873f57.png using Guid(5d8ff31fb17c0e44a9236df2f6807bb3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '79dee29a5cff183e6dc1ff07947dfe00') in 0.02047 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 419.818826 seconds.
  path: Assets/4289ec7a-1033-4c9f-b84f-2c6e420d6459.png
  artifactKey: Guid(3409a295a34c3064aaf4e72eea52877a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/4289ec7a-1033-4c9f-b84f-2c6e420d6459.png using Guid(3409a295a34c3064aaf4e72eea52877a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cb0fea1a68bc671b3fbfa31967acd535') in 0.0220432 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 59.836343 seconds.
  path: Assets/6d1abb68-4ab4-492d-90da-df29b3aa0ab5.png
  artifactKey: Guid(3b3ef58928d003745b2ac58618e11311) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/6d1abb68-4ab4-492d-90da-df29b3aa0ab5.png using Guid(3b3ef58928d003745b2ac58618e11311) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '58ba3836df6a284235a60a45c6e3a290') in 0.0256572 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 108.408491 seconds.
  path: Assets/c34fe758-d43c-407a-8dcd-3c1cafe64def.png
  artifactKey: Guid(c1f6ef6107a52a8448ed8c0baac7797c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/c34fe758-d43c-407a-8dcd-3c1cafe64def.png using Guid(c1f6ef6107a52a8448ed8c0baac7797c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc973e16f70c48b54bcab021a20b114e') in 0.0201433 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 267.724593 seconds.
  path: Assets/form land.png
  artifactKey: Guid(1fd916c5f22a36c4695bad846cbfd53a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/form land.png using Guid(1fd916c5f22a36c4695bad846cbfd53a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd95b3e241757537ffbceac2055199887') in 0.0235609 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 286.820866 seconds.
  path: Assets/green field.png
  artifactKey: Guid(4b44bb8c75a55e34e9292c32f6d54e2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/green field.png using Guid(4b44bb8c75a55e34e9292c32f6d54e2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e66e967ef729fb44c6621cf86e1be398') in 0.0221147 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 369.771687 seconds.
  path: Assets/plough shoil.png
  artifactKey: Guid(7d8ab83679dff01409c0342af7f0f8fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/plough shoil.png using Guid(7d8ab83679dff01409c0342af7f0f8fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '600f937ebaa54ac45e6a772da60247c7') in 0.0392842 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 406.757130 seconds.
  path: Assets/soil.png
  artifactKey: Guid(eb69d34b5536cb847b8c5834e74f1889) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/soil.png using Guid(eb69d34b5536cb847b8c5834e74f1889) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '61821ae1c4d7011a76f2a23f339505bd') in 0.0282692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 255.789454 seconds.
  path: Assets/04db5772-fdb8-421d-9903-fce8dd932c4b.png
  artifactKey: Guid(2a39bb88103c55b47bf6930bd77c1418) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/04db5772-fdb8-421d-9903-fce8dd932c4b.png using Guid(2a39bb88103c55b47bf6930bd77c1418) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '61b5bea7b7612a0e56112de92277c596') in 0.02971 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 157.448801 seconds.
  path: Assets/092fcba2-a9bf-484e-930b-0f82c3dbca67.png
  artifactKey: Guid(a0d1550fa3df84742bea1940b8e09d69) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/092fcba2-a9bf-484e-930b-0f82c3dbca67.png using Guid(a0d1550fa3df84742bea1940b8e09d69) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd62376b9fc14c605a2e63e5ba0a1df05') in 0.0214569 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 67.366006 seconds.
  path: Assets/imgi_59_Soil-conservation-practices-2-min.jpeg
  artifactKey: Guid(e1d9984dda9268945bf4f967f5b2f654) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/imgi_59_Soil-conservation-practices-2-min.jpeg using Guid(e1d9984dda9268945bf4f967f5b2f654) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc10078fdbaef194f4ba5099d546fd85') in 0.0305231 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  5.472 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 2.96 ms, found 5 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.622 seconds
Domain Reload Profiling: 7098ms
	BeginReloadAssembly (1080ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (325ms)
	RebuildCommonClasses (73ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (4229ms)
		LoadAssemblies (4126ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (662ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (616ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1624ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1369ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (294ms)
			ProcessInitializeOnLoadAttributes (691ms)
			ProcessInitializeOnLoadMethodAttributes (345ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 2.82 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.06 ms.
Unloading 30 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6841 unused Assets / (5.2 MB). Loaded Objects now: 7361.
Memory consumption went from 221.9 MB to 216.7 MB.
Total: 22.503500 ms (FindLiveObjects: 1.416400 ms CreateObjectMapping: 1.049600 ms MarkObjects: 13.208400 ms  DeleteObjects: 6.826000 ms)

Prepare: number of updated asset objects reloaded= 0
